#pragma once

/*
 * CTextureManager.h - RESOURCE ENHANCED
 * NexusPro RF Online Zone Server
 * 
 * Texture loading and caching management system
 * Enhanced with Emergency Fix Methodology for RF Online compatibility
 * 
 * Original functionality based on decompiled RF Online server code
 * Enhanced with modern C++ standards and comprehensive texture management
 */

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CMainThread;
class CResourceManager;
class CFileManager;
class CSystemManager;

// Texture formats
enum TextureFormat {
    TEXTURE_FORMAT_UNKNOWN = 0,
    TEXTURE_FORMAT_DDS = 1,
    TEXTURE_FORMAT_BMP = 2,
    TEXTURE_FORMAT_TGA = 3,
    TEXTURE_FORMAT_JPG = 4,
    TEXTURE_FORMAT_PNG = 5,
    TEXTURE_FORMAT_R3T = 6  // RF Online custom format
};

// Texture types
enum TextureType {
    TEXTURE_TYPE_DIFFUSE = 0,
    TEXTURE_TYPE_NORMAL = 1,
    TEXTURE_TYPE_SPECULAR = 2,
    TEXTURE_TYPE_ENVIRONMENT = 3,
    TEXTURE_TYPE_LIGHTMAP = 4,
    TEXTURE_TYPE_SHADOWMAP = 5,
    TEXTURE_TYPE_UI = 6,
    TEXTURE_TYPE_EFFECT = 7,
    TEXTURE_TYPE_SYSTEM = 8
};

// Texture quality levels
enum TextureQuality {
    TEXTURE_QUALITY_LOW = 0,
    TEXTURE_QUALITY_MEDIUM = 1,
    TEXTURE_QUALITY_HIGH = 2,
    TEXTURE_QUALITY_ULTRA = 3
};

// Texture loading result
enum TextureResult {
    TEXTURE_SUCCESS = 0,
    TEXTURE_ERROR_NOT_FOUND = 1,
    TEXTURE_ERROR_INVALID_FORMAT = 2,
    TEXTURE_ERROR_OUT_OF_MEMORY = 3,
    TEXTURE_ERROR_CORRUPTED = 4,
    TEXTURE_ERROR_UNSUPPORTED = 5,
    TEXTURE_ERROR_UNKNOWN = 6
};

// Texture information
struct TextureInfo {
    char szTextureName[256];
    char szFilePath[MAX_PATH];
    TextureFormat format;
    TextureType type;
    TextureQuality quality;
    DWORD dwWidth;
    DWORD dwHeight;
    DWORD dwMipLevels;
    DWORD dwFileSize;
    DWORD dwMemorySize;
    DWORD dwLastAccess;
    DWORD dwAccessCount;
    DWORD dwLoadTime;
    bool bLoaded;
    bool bCached;
    bool bCompressed;
    bool bMipMapped;
    void* pTextureData;
    void* pD3DTexture;  // Direct3D texture object
};

// Texture cache entry
struct TextureCacheEntry {
    TextureInfo info;
    DWORD dwCacheTime;
    DWORD dwLastUsed;
    bool bLocked;
    bool bDirty;
};

// Texture manager configuration
struct TextureManagerConfig {
    bool bEnabled;
    bool bCacheEnabled;
    bool bCompressionEnabled;
    bool bMipMappingEnabled;
    bool bAsyncLoadingEnabled;
    bool bQualityScalingEnabled;
    TextureQuality defaultQuality;
    DWORD dwMaxCacheSize;
    DWORD dwMaxCacheEntries;
    DWORD dwCacheTimeout;
    DWORD dwMaxTextureSize;
    DWORD dwMinTextureSize;
    DWORD dwCompressionLevel;
    char szTextureRootPath[MAX_PATH];
    char szSystemTexturePath[MAX_PATH];
    char szUITexturePath[MAX_PATH];
    char szEffectTexturePath[MAX_PATH];
};

// Texture manager statistics
struct TextureManagerStatistics {
    DWORD dwTotalTextures;
    DWORD dwLoadedTextures;
    DWORD dwCachedTextures;
    DWORD dwTextureLoads;
    DWORD dwTextureUnloads;
    DWORD dwCacheHits;
    DWORD dwCacheMisses;
    DWORD dwTotalMemoryUsed;
    DWORD dwCacheMemoryUsed;
    DWORD dwVideoMemoryUsed;
    DWORD dwAverageLoadTime;
    DWORD dwMaxLoadTime;
    DWORD dwLastLoadTime;
    DWORD dwCompressionSavings;
    DWORD dwMipMapLevelsGenerated;
};

/*
 * CTextureManager - Texture loading and caching management
 * Manages all texture operations, loading, caching, and optimization
 * Enhanced with comprehensive texture management and RF Online compatibility
 */
class CTextureManager {
public:
    // Singleton access
    static CTextureManager& Instance();

    // Core lifecycle methods
    bool Initialize();
    void Shutdown();
    void Update();

    // Texture loading methods
    TextureResult LoadTexture(const char* szTextureName, const char* szFilePath);
    TextureResult LoadTexture(const char* szTextureName, const char* szFilePath, TextureType type);
    TextureResult LoadTexture(const char* szTextureName, const char* szFilePath, TextureType type, TextureQuality quality);
    TextureResult LoadSystemTexture(const char* szTextureName);
    TextureResult LoadUITexture(const char* szTextureName);
    TextureResult LoadEffectTexture(const char* szTextureName);

    // Texture access methods
    void* GetTexture(const char* szTextureName);
    void* GetD3DTexture(const char* szTextureName);
    bool GetTextureInfo(const char* szTextureName, TextureInfo& info);
    bool IsTextureLoaded(const char* szTextureName);

    // Texture management methods
    void UnloadTexture(const char* szTextureName);
    void UnloadAllTextures();
    void UnloadTexturesByType(TextureType type);
    void ReloadTexture(const char* szTextureName);
    void ReloadAllTextures();

    // Texture caching methods
    bool CacheTexture(const char* szTextureName);
    void UncacheTexture(const char* szTextureName);
    bool IsTextureCached(const char* szTextureName);
    void FlushCache();
    void OptimizeCache();

    // Texture processing methods
    bool GenerateMipMaps(const char* szTextureName);
    bool CompressTexture(const char* szTextureName);
    bool ResizeTexture(const char* szTextureName, DWORD dwNewWidth, DWORD dwNewHeight);
    bool ConvertTextureFormat(const char* szTextureName, TextureFormat newFormat);

    // Texture utility methods
    TextureFormat DetectTextureFormat(const char* szFilePath);
    DWORD CalculateTextureMemorySize(DWORD dwWidth, DWORD dwHeight, TextureFormat format, DWORD dwMipLevels);
    bool ValidateTextureFile(const char* szFilePath);
    bool CreateTextureFromMemory(const char* szTextureName, const void* pData, DWORD dwSize);

    // System texture methods (based on decompiled RestoreSystemTexture/ReleaseSystemTexture)
    bool InitializeSystemTextures();
    void ReleaseSystemTextures();
    bool RestoreSystemTextures();
    bool LoadSystemLogo();
    bool LoadSystemDLight();

    // Configuration and statistics
    const TextureManagerConfig& GetConfig() const { return m_Config; }
    const TextureManagerStatistics& GetStatistics() const { return m_Statistics; }
    void ResetStatistics();

    // Utility methods
    bool IsTextureManagerEnabled() const { return m_bInitialized && m_Config.bEnabled; }
    void LogTextureOperation(const char* szOperation, const char* szTextureName, TextureResult result);

private:
    // Private constructor for singleton
    CTextureManager();
    ~CTextureManager();

    // Prevent copying
    CTextureManager(const CTextureManager&) = delete;
    CTextureManager& operator=(const CTextureManager&) = delete;

    // Internal methods
    bool LoadConfiguration();
    bool InitializeD3D();
    bool InitializeCache();
    void ShutdownD3D();
    void ShutdownCache();
    TextureResult LoadTextureFromFile(const char* szTextureName, const char* szFilePath, TextureType type, TextureQuality quality);
    TextureResult ProcessTextureData(TextureInfo& info, const void* pData, DWORD dwSize);
    void UpdateStatistics();
    void UpdateCache();

    // Cache management
    TextureCacheEntry* FindCacheEntry(const char* szTextureName);
    TextureCacheEntry* CreateCacheEntry(const TextureInfo& info);
    void RemoveCacheEntry(const char* szTextureName);
    void CleanupExpiredCacheEntries();

    // Format detection and processing
    TextureFormat DetectFormatFromHeader(const void* pData, DWORD dwSize);
    bool ProcessDDSTexture(TextureInfo& info, const void* pData, DWORD dwSize);
    bool ProcessBMPTexture(TextureInfo& info, const void* pData, DWORD dwSize);
    bool ProcessTGATexture(TextureInfo& info, const void* pData, DWORD dwSize);
    bool ProcessR3TTexture(TextureInfo& info, const void* pData, DWORD dwSize);

    // Member variables
    static CTextureManager* s_pInstance;
    static std::mutex s_InstanceMutex;

    bool m_bInitialized;
    bool m_bD3DInitialized;
    bool m_bCacheActive;
    TextureManagerConfig m_Config;
    TextureManagerStatistics m_Statistics;

    // Texture storage
    std::map<std::string, TextureInfo> m_Textures;
    std::map<std::string, TextureCacheEntry> m_TextureCache;
    DWORD m_dwCurrentCacheSize;
    DWORD m_dwCacheEntryCount;

    // System textures (based on decompiled code)
    void* m_pSystemLogo;      // qword_184A79C18
    void* m_pSystemDLight;    // qword_184A79C20

    // Synchronization
    mutable std::mutex m_ConfigMutex;
    mutable std::mutex m_StatisticsMutex;
    mutable std::mutex m_TextureMutex;
    mutable std::mutex m_CacheMutex;

    // Performance tracking
    DWORD m_dwLastUpdateTime;
    DWORD m_dwUpdateInterval;
    DWORD m_dwLoadStartTime;
};
