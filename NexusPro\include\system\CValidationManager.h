#pragma once

/*
 * CValidationManager.h - SECURITY ENHANCED
 * NexusPro RF Online Zone Server
 * 
 * Input and data validation management system
 * Enhanced with Emergency Fix Methodology for RF Online compatibility
 * 
 * Original functionality based on decompiled RF Online server code
 * Enhanced with modern C++ standards and comprehensive validation measures
 */

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CMainThread;
class CNetworkManager;
class CSecurityManager;
class CSystemManager;

// Validation result types
enum ValidationResult {
    VALIDATION_SUCCESS = 0,
    VALIDATION_FAILED = 1,
    VALIDATION_INVALID_INPUT = 2,
    VALIDATION_BUFFER_OVERFLOW = 3,
    VALIDATION_FORMAT_ERROR = 4,
    VALIDATION_RANGE_ERROR = 5,
    VALIDATION_NULL_POINTER = 6,
    VALIDATION_ENCODING_ERROR = 7,
    VALIDATION_CHECKSUM_ERROR = 8,
    VALIDATION_SIGNATURE_ERROR = 9
};

// Validation types
enum ValidationType {
    VALIDATION_TYPE_STRING = 0,
    VALIDATION_TYPE_INTEGER = 1,
    VALIDATION_TYPE_FLOAT = 2,
    VALIDATION_TYPE_PACKET = 3,
    VALIDATION_TYPE_FILE = 4,
    VALIDATION_TYPE_MEMORY = 5,
    VALIDATION_TYPE_CHECKSUM = 6,
    VALIDATION_TYPE_SIGNATURE = 7,
    VALIDATION_TYPE_ENCODING = 8
};

// String validation parameters
struct StringValidationParams {
    DWORD dwMinLength;
    DWORD dwMaxLength;
    bool bAllowEmpty;
    bool bAllowNull;
    bool bCheckEncoding;
    bool bCheckSpecialChars;
    bool bCheckSQLInjection;
    bool bCheckXSS;
    char szAllowedChars[256];
    char szForbiddenChars[256];
};

// Integer validation parameters
struct IntegerValidationParams {
    __int64 nMinValue;
    __int64 nMaxValue;
    bool bAllowNegative;
    bool bAllowZero;
    bool bCheckOverflow;
};

// Float validation parameters
struct FloatValidationParams {
    double dMinValue;
    double dMaxValue;
    bool bAllowNegative;
    bool bAllowZero;
    bool bCheckPrecision;
    DWORD dwMaxDecimalPlaces;
};

// Packet validation parameters
struct PacketValidationParams {
    DWORD dwMinSize;
    DWORD dwMaxSize;
    DWORD dwExpectedType;
    bool bCheckChecksum;
    bool bCheckSequence;
    bool bCheckTimestamp;
    bool bCheckSignature;
};

// File validation parameters
struct FileValidationParams {
    DWORD dwMaxSize;
    bool bCheckExtension;
    bool bCheckMagicBytes;
    bool bCheckChecksum;
    bool bCheckSignature;
    char szAllowedExtensions[512];
    BYTE byMagicBytes[16];
    DWORD dwMagicBytesLength;
};

// Validation configuration
struct ValidationConfig {
    bool bEnabled;
    bool bStrictMode;
    bool bLogValidationErrors;
    bool bLogValidationWarnings;
    bool bBlockInvalidInput;
    bool bSanitizeInput;
    DWORD dwMaxValidationTime;
    DWORD dwMaxConcurrentValidations;
    StringValidationParams stringParams;
    IntegerValidationParams integerParams;
    FloatValidationParams floatParams;
    PacketValidationParams packetParams;
    FileValidationParams fileParams;
};

// Validation statistics
struct ValidationStatistics {
    DWORD dwTotalValidations;
    DWORD dwSuccessfulValidations;
    DWORD dwFailedValidations;
    DWORD dwStringValidations;
    DWORD dwIntegerValidations;
    DWORD dwFloatValidations;
    DWORD dwPacketValidations;
    DWORD dwFileValidations;
    DWORD dwMemoryValidations;
    DWORD dwChecksumValidations;
    DWORD dwSignatureValidations;
    DWORD dwEncodingValidations;
    DWORD dwBlockedInputs;
    DWORD dwSanitizedInputs;
    DWORD dwAverageValidationTime;
    DWORD dwMaxValidationTime;
    DWORD dwLastValidationTime;
};

/*
 * CValidationManager - Input and data validation management
 * Manages all input validation and data sanitization
 * Enhanced with comprehensive security measures and RF Online compatibility
 */
class CValidationManager {
public:
    // Singleton access
    static CValidationManager& Instance();

    // Core lifecycle methods
    bool Initialize();
    void Shutdown();
    void Update();

    // String validation methods
    ValidationResult ValidateString(const char* szInput, const StringValidationParams& params);
    ValidationResult ValidateString(const char* szInput, DWORD dwMaxLength = 256);
    ValidationResult ValidatePlayerName(const char* szPlayerName);
    ValidationResult ValidateGuildName(const char* szGuildName);
    ValidationResult ValidateChatMessage(const char* szMessage);
    ValidationResult ValidateCommand(const char* szCommand);

    // Numeric validation methods
    ValidationResult ValidateInteger(__int64 nValue, const IntegerValidationParams& params);
    ValidationResult ValidateInteger(__int64 nValue, __int64 nMin, __int64 nMax);
    ValidationResult ValidateFloat(double dValue, const FloatValidationParams& params);
    ValidationResult ValidateFloat(double dValue, double dMin, double dMax);

    // Packet validation methods
    ValidationResult ValidatePacket(const void* pPacketData, DWORD dwSize, const PacketValidationParams& params);
    ValidationResult ValidatePacketHeader(const void* pHeader, DWORD dwSize);
    ValidationResult ValidatePacketChecksum(const void* pPacketData, DWORD dwSize, DWORD dwChecksum);

    // File validation methods
    ValidationResult ValidateFile(const char* szFilePath, const FileValidationParams& params);
    ValidationResult ValidateFileExtension(const char* szFilePath, const char* szAllowedExtensions);
    ValidationResult ValidateFileSize(const char* szFilePath, DWORD dwMaxSize);

    // Memory validation methods
    ValidationResult ValidateMemoryBuffer(const void* pBuffer, DWORD dwSize);
    ValidationResult ValidatePointer(const void* pPointer);
    ValidationResult ValidateMemoryRange(const void* pStart, DWORD dwSize);

    // Checksum and signature validation
    ValidationResult ValidateChecksum(const void* pData, DWORD dwSize, DWORD dwExpectedChecksum);
    ValidationResult ValidateSignature(const void* pData, DWORD dwSize, const void* pSignature, DWORD dwSignatureSize);

    // Encoding validation
    ValidationResult ValidateUTF8(const char* szInput);
    ValidationResult ValidateASCII(const char* szInput);
    ValidationResult ValidateEncoding(const char* szInput, DWORD dwCodePage);

    // Input sanitization methods
    bool SanitizeString(char* szInput, DWORD dwBufferSize);
    bool SanitizePlayerName(char* szPlayerName, DWORD dwBufferSize);
    bool SanitizeChatMessage(char* szMessage, DWORD dwBufferSize);
    bool RemoveSpecialCharacters(char* szInput, DWORD dwBufferSize);
    bool EscapeSpecialCharacters(char* szInput, DWORD dwBufferSize, DWORD dwOutputSize);

    // Security validation methods
    bool CheckSQLInjection(const char* szInput);
    bool CheckXSSAttempt(const char* szInput);
    bool CheckBufferOverflow(const char* szInput, DWORD dwMaxLength);
    bool CheckFormatString(const char* szInput);

    // Configuration and statistics
    const ValidationConfig& GetConfig() const { return m_Config; }
    const ValidationStatistics& GetStatistics() const { return m_Statistics; }
    void ResetStatistics();

    // Utility methods
    bool IsValidationEnabled() const { return m_bInitialized && m_Config.bEnabled; }
    void LogValidationError(ValidationResult result, const char* szDetails);
    void LogValidationWarning(ValidationResult result, const char* szDetails);

private:
    // Private constructor for singleton
    CValidationManager();
    ~CValidationManager();

    // Prevent copying
    CValidationManager(const CValidationManager&) = delete;
    CValidationManager& operator=(const CValidationManager&) = delete;

    // Internal methods
    bool LoadConfiguration();
    void UpdateStatistics();
    ValidationResult ProcessValidationResult(ValidationResult result, ValidationType type);

    // Member variables
    static CValidationManager* s_pInstance;
    static std::mutex s_InstanceMutex;

    bool m_bInitialized;
    ValidationConfig m_Config;
    ValidationStatistics m_Statistics;

    // Synchronization
    mutable std::mutex m_ConfigMutex;
    mutable std::mutex m_StatisticsMutex;
    mutable std::mutex m_ValidationMutex;

    // Performance tracking
    DWORD m_dwLastUpdateTime;
    DWORD m_dwUpdateInterval;
    DWORD m_dwValidationStartTime;
    DWORD m_dwConcurrentValidations;
};
