#pragma once

/**
 * @file CSpecialEventManager.h
 * @brief Special Event Management System for NexusPro
 * 
 * Manages special events including class refinement events, experience events,
 * item events, seasonal events, and other special game mechanics. Handles
 * event validation, participant tracking, and event-specific rewards.
 * 
 * Original decompiled functions:
 * - <PERSON><PERSON>vent (RFEventBase): 0x1403294D0
 * - DoEvent (RFEvent_ClassRefine): 0x140328CD0
 * - CanDoEvent: 0x140328BC0
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @category Player Extended Systems - Phase 2 Batch 8
 * @note STRICT ADHERENCE TO DECOMPILED SOURCE: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */

#include "../common/WindowsTypes.h"
#include "../common/RFProtocol.h"
#include "../common/Stubs.h"
#include "../event/RFEventBase.h"
#include "../event/RFEvent_ClassRefine.h"
#include <memory>
#include <mutex>
#include <vector>
#include <unordered_map>

// Forward declarations
class CPlayer;
class RFEventBase;
class RFEvent_ClassRefine;

/**
 * @enum EventType
 * @brief Types of special events
 */
enum EventType {
    EVENT_NONE = 0,                                  ///< No event
    EVENT_CLASS_REFINE = 1,                          ///< Class refinement event
    EVENT_EXPERIENCE_BOOST = 2,                      ///< Experience boost event
    EVENT_ITEM_DROP_RATE = 3,                        ///< Item drop rate event
    EVENT_SEASONAL = 4,                              ///< Seasonal event
    EVENT_PVP_TOURNAMENT = 5,                        ///< PvP tournament event
    EVENT_GUILD_BATTLE = 6,                          ///< Guild battle event
    EVENT_MINING_BOOST = 7,                          ///< Mining boost event
    EVENT_CRAFTING_BOOST = 8,                        ///< Crafting boost event
    EVENT_SPECIAL_DUNGEON = 9,                       ///< Special dungeon event
    EVENT_HOLIDAY = 10                               ///< Holiday event
};

/**
 * @enum EventStatus
 * @brief Status of events
 */
enum EventStatus {
    STATUS_INACTIVE = 0,                             ///< Event is inactive
    STATUS_ACTIVE = 1,                               ///< Event is active
    STATUS_PAUSED = 2,                               ///< Event is paused
    STATUS_ENDED = 3,                                ///< Event has ended
    STATUS_MAINTENANCE = 4                           ///< Event under maintenance
};

/**
 * @struct EventParticipant
 * @brief Participant data for events
 */
struct EventParticipant {
    DWORD dwPlayerSerial;                            ///< Player serial number
    int nCurRefineCnt;                               ///< Current refinement count (for class refine events)
    bool bChange;                                    ///< Change flag
    DWORD dwParticipationTime;                       ///< Time of participation
    DWORD dwLastActivity;                            ///< Last activity timestamp
    int nEventScore;                                 ///< Event-specific score
    
    /**
     * @brief Default constructor
     */
    EventParticipant();
    
    /**
     * @brief Constructor with player serial
     */
    EventParticipant(DWORD playerSerial);
};

/**
 * @struct EventConfiguration
 * @brief Configuration data for events
 */
struct EventConfiguration {
    EventType type;                                  ///< Type of event
    EventStatus status;                              ///< Current status
    DWORD dwStartTime;                               ///< Event start time
    DWORD dwEndTime;                                 ///< Event end time
    DWORD dwDuration;                                ///< Event duration in seconds
    double dMultiplier;                              ///< Event multiplier (for boost events)
    int nMaxParticipants;                            ///< Maximum participants
    int nCurrentParticipants;                        ///< Current participant count
    std::string strEventName;                        ///< Event name
    std::string strDescription;                      ///< Event description
    
    /**
     * @brief Default constructor
     */
    EventConfiguration();
    
    /**
     * @brief Constructor with parameters
     */
    EventConfiguration(EventType eventType, const std::string& name, DWORD duration);
};

/**
 * @class CSpecialEventManager
 * @brief Manages special events and player participation
 * 
 * This class handles all aspects of special event functionality including:
 * - Event lifecycle management (start, pause, end)
 * - Player participation tracking and validation
 * - Event-specific logic execution (class refine, experience boost, etc.)
 * - Reward distribution and event completion tracking
 * - Multi-event coordination and conflict resolution
 * - Event configuration and parameter management
 * 
 * Based on decompiled source at: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */
class CSpecialEventManager {
public:
    /**
     * @brief Default constructor
     * Initializes special event manager with RAII resource management
     */
    CSpecialEventManager();

    /**
     * @brief Virtual destructor for proper cleanup
     * Ensures all event operations are properly completed and resources released
     */
    virtual ~CSpecialEventManager();

    // === Core Event Operations ===
    
    /**
     * @brief Executes event logic for player
     * 
     * Base event execution that delegates to specific event implementations.
     * Performs basic validation and participant tracking.
     * 
     * @param pEvent Event to execute
     * @param pPlayer Player participating in event
     * @return Event execution result (0 = success, non-zero = error)
     * 
     * Original address: 0x1403294D0
     * Function: ?DoEvent@RFEventBase@@UEAAHPEAVCPlayer@@@Z
     */
    int DoEvent(RFEventBase *pEvent, CPlayer *pPlayer);

    /**
     * @brief Executes class refinement event logic
     * 
     * Handles class refinement event including:
     * - Event validation and participant checking
     * - Class initialization and refinement processing
     * - Participant count tracking and change flagging
     * - Debug memory initialization (0xCCCCCCCC pattern)
     * 
     * @param pEvent Class refinement event
     * @param pPlayer Player participating in event
     * @return Event execution result (0 = success, non-zero = error)
     * 
     * Original address: 0x140328CD0
     * Function: ?DoEvent@RFEvent_ClassRefine@@UEAAHPEAVCPlayer@@@Z
     */
    int DoEvent_ClassRefine(RFEvent_ClassRefine *pEvent, CPlayer *pPlayer);

    /**
     * @brief Validates if player can participate in event
     * 
     * Checks event prerequisites and player eligibility.
     * 
     * @param pEvent Event to validate
     * @param pPlayer Player to validate
     * @return true if player can participate
     * 
     * Original address: 0x140328BC0
     * Function: ?CanDoEvent@RFEvent_ClassRefine@@EEAAHPEBVCPlayer@@@Z
     */
    bool CanDoEvent(RFEventBase *pEvent, CPlayer *pPlayer) const;

    // === Event Management ===
    
    /**
     * @brief Starts a new event
     * @param eventConfig Event configuration
     * @return true if event was started successfully
     */
    bool StartEvent(const EventConfiguration &eventConfig);

    /**
     * @brief Stops an active event
     * @param eventType Type of event to stop
     * @return true if event was stopped successfully
     */
    bool StopEvent(EventType eventType);

    /**
     * @brief Pauses an active event
     * @param eventType Type of event to pause
     * @return true if event was paused successfully
     */
    bool PauseEvent(EventType eventType);

    /**
     * @brief Resumes a paused event
     * @param eventType Type of event to resume
     * @return true if event was resumed successfully
     */
    bool ResumeEvent(EventType eventType);

    /**
     * @brief Gets current event status
     * @param eventType Type of event to check
     * @return Current event status
     */
    EventStatus GetEventStatus(EventType eventType) const;

    // === Participant Management ===
    
    /**
     * @brief Registers player for event participation
     * @param eventType Type of event
     * @param pPlayer Player to register
     * @return true if registration was successful
     */
    bool RegisterParticipant(EventType eventType, CPlayer *pPlayer);

    /**
     * @brief Unregisters player from event
     * @param eventType Type of event
     * @param pPlayer Player to unregister
     * @return true if unregistration was successful
     */
    bool UnregisterParticipant(EventType eventType, CPlayer *pPlayer);

    /**
     * @brief Gets participant data for player
     * @param eventType Type of event
     * @param pPlayer Player to get data for
     * @return Pointer to participant data, nullptr if not found
     */
    EventParticipant* GetParticipant(EventType eventType, CPlayer *pPlayer);

    /**
     * @brief Gets total participant count for event
     * @param eventType Type of event
     * @return Number of participants
     */
    int GetParticipantCount(EventType eventType) const;

    // === Event-Specific Operations ===
    
    /**
     * @brief Processes experience boost event
     * @param pPlayer Player gaining experience
     * @param dBaseExp Base experience amount
     * @return Modified experience with event bonus
     */
    double ProcessExperienceBoostEvent(CPlayer *pPlayer, double dBaseExp);

    /**
     * @brief Processes item drop rate event
     * @param pPlayer Player receiving item drop
     * @param dBaseRate Base drop rate
     * @return Modified drop rate with event bonus
     */
    double ProcessItemDropRateEvent(CPlayer *pPlayer, double dBaseRate);

    /**
     * @brief Processes mining boost event
     * @param pPlayer Player mining
     * @param nBaseYield Base mining yield
     * @return Modified yield with event bonus
     */
    int ProcessMiningBoostEvent(CPlayer *pPlayer, int nBaseYield);

    /**
     * @brief Processes crafting boost event
     * @param pPlayer Player crafting
     * @param dBaseSuccessRate Base success rate
     * @return Modified success rate with event bonus
     */
    double ProcessCraftingBoostEvent(CPlayer *pPlayer, double dBaseSuccessRate);

    // === Event Configuration ===
    
    /**
     * @brief Sets event configuration
     * @param eventType Type of event
     * @param config New configuration
     * @return true if configuration was set successfully
     */
    bool SetEventConfiguration(EventType eventType, const EventConfiguration &config);

    /**
     * @brief Gets event configuration
     * @param eventType Type of event
     * @return Event configuration
     */
    EventConfiguration GetEventConfiguration(EventType eventType) const;

    /**
     * @brief Updates event multiplier
     * @param eventType Type of event
     * @param dMultiplier New multiplier value
     * @return true if multiplier was updated successfully
     */
    bool UpdateEventMultiplier(EventType eventType, double dMultiplier);

    // === Utility Methods ===
    
    /**
     * @brief Checks if any events are currently active
     * @return true if at least one event is active
     */
    bool HasActiveEvents() const;

    /**
     * @brief Gets list of active events
     * @return Vector of active event types
     */
    std::vector<EventType> GetActiveEvents() const;

    /**
     * @brief Validates event time constraints
     * @param eventType Type of event
     * @return true if event is within valid time window
     */
    bool ValidateEventTime(EventType eventType) const;

    /**
     * @brief Updates event timers and status
     */
    void UpdateEventTimers();

protected:
    // === Core Event Data ===
    std::unordered_map<EventType, EventConfiguration> m_eventConfigs; ///< Event configurations
    std::unordered_map<EventType, std::vector<EventParticipant>> m_participants; ///< Event participants
    
    // === System State ===
    bool m_bInitialized;                             ///< Initialization status
    DWORD m_dwLastUpdate;                            ///< Last update timestamp
    
    // === Configuration Constants ===
    static constexpr int MAX_PARTICIPANTS_PER_EVENT = 1000; ///< Maximum participants per event
    static constexpr double DEFAULT_EXPERIENCE_MULTIPLIER = 2.0; ///< Default experience boost
    static constexpr double DEFAULT_DROP_RATE_MULTIPLIER = 1.5; ///< Default drop rate boost
    static constexpr double DEFAULT_MINING_MULTIPLIER = 1.3; ///< Default mining boost
    static constexpr double DEFAULT_CRAFTING_MULTIPLIER = 1.2; ///< Default crafting boost
    static constexpr DWORD EVENT_UPDATE_INTERVAL = 60000; ///< Event update interval (1 minute)
    
    // === Security and Validation ===
    mutable std::mutex m_eventMutex;                 ///< Mutex for thread-safe operations

private:
    // === Helper Methods ===
    
    /**
     * @brief Performs debug memory initialization
     * 
     * Initializes memory with debug pattern (0xCCCCCCCC) as per
     * decompiled source implementation.
     */
    void InitializeDebugMemory();

    /**
     * @brief Validates event parameters
     * @param eventType Type of event
     * @param pPlayer Player to validate
     * @return true if parameters are valid
     */
    bool ValidateEventParameters(EventType eventType, CPlayer *pPlayer) const;

    /**
     * @brief Processes event completion
     * @param eventType Type of event
     * @param pPlayer Player completing event
     */
    void ProcessEventCompletion(EventType eventType, CPlayer *pPlayer);

    /**
     * @brief Logs event operation for audit
     * @param pPlayer Player performing operation
     * @param strOperation Operation description
     * @param eventType Optional event type
     */
    void LogEventOperation(CPlayer* pPlayer, const char* strOperation, EventType eventType = EVENT_NONE) const;

    /**
     * @brief Validates player state for event participation
     * @param pPlayer Player to validate
     * @return true if player is in valid state
     */
    bool ValidatePlayerStateForEvent(CPlayer *pPlayer) const;

    /**
     * @brief Applies event effects to player
     * @param eventType Type of event
     * @param pPlayer Player to apply effects to
     */
    void ApplyEventEffects(EventType eventType, CPlayer *pPlayer);

    /**
     * @brief Removes event effects from player
     * @param eventType Type of event
     * @param pPlayer Player to remove effects from
     */
    void RemoveEventEffects(EventType eventType, CPlayer *pPlayer);

    /**
     * @brief Distributes event rewards
     * @param eventType Type of event
     * @param pPlayer Player to reward
     */
    void DistributeEventRewards(EventType eventType, CPlayer *pPlayer);

    /**
     * @brief Checks event conflicts
     * @param eventType Type of event to check
     * @return true if event conflicts with active events
     */
    bool CheckEventConflicts(EventType eventType) const;
};
