#pragma once

/*
 * CIntegrityManager.h - SECURITY ENHANCED
 * NexusPro RF Online Zone Server
 * 
 * File and memory integrity management system
 * Enhanced with Emergency Fix Methodology for RF Online compatibility
 * 
 * Original functionality based on decompiled RF Online server code
 * Enhanced with modern C++ standards and comprehensive integrity measures
 */

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CMainThread;
class CNetworkManager;
class CSecurityManager;
class CSystemManager;

// Integrity check types
enum IntegrityCheckType {
    INTEGRITY_FILE_CHECKSUM = 0,
    INTEGRITY_FILE_SIGNATURE = 1,
    INTEGRITY_MEMORY_CHECKSUM = 2,
    INTEGRITY_MEMORY_PATTERN = 3,
    INTEGRITY_SYSTEM_FILES = 4,
    INTEGRITY_GAME_FILES = 5,
    INTEGRITY_EXECUTABLE = 6,
    INTEGRITY_LIBRARY = 7,
    INTEGRITY_CONFIGURATION = 8
};

// Integrity check result
enum IntegrityResult {
    INTEGRITY_SUCCESS = 0,
    INTEGRITY_FAILED = 1,
    INTEGRITY_FILE_NOT_FOUND = 2,
    INTEGRITY_CHECKSUM_MISMATCH = 3,
    INTEGRITY_SIGNATURE_INVALID = 4,
    INTEGRITY_MEMORY_CORRUPTED = 5,
    INTEGRITY_PATTERN_MISMATCH = 6,
    INTEGRITY_ACCESS_DENIED = 7,
    INTEGRITY_UNKNOWN_ERROR = 8
};

// File integrity information
struct FileIntegrityInfo {
    char szFilePath[MAX_PATH];
    DWORD dwFileSize;
    DWORD dwChecksum;
    FILETIME ftLastModified;
    DWORD dwAttributes;
    bool bSignatureValid;
    bool bChecksumValid;
    IntegrityResult lastResult;
    DWORD dwLastCheckTime;
};

// Memory integrity information
struct MemoryIntegrityInfo {
    void* pMemoryAddress;
    DWORD dwMemorySize;
    DWORD dwChecksum;
    DWORD dwExpectedPattern;
    bool bPatternValid;
    bool bChecksumValid;
    IntegrityResult lastResult;
    DWORD dwLastCheckTime;
};

// Integrity check configuration
struct IntegrityConfig {
    bool bEnabled;
    bool bFileIntegrityEnabled;
    bool bMemoryIntegrityEnabled;
    bool bSystemFileCheck;
    bool bGameFileCheck;
    bool bExecutableCheck;
    bool bLibraryCheck;
    bool bConfigurationCheck;
    bool bRealTimeMonitoring;
    bool bPeriodicChecks;
    DWORD dwCheckInterval;
    DWORD dwMaxConcurrentChecks;
    DWORD dwCheckTimeout;
    bool bLogIntegrityErrors;
    bool bLogIntegrityWarnings;
    bool bBlockOnFailure;
    bool bRepairOnFailure;
};

// Integrity statistics
struct IntegrityStatistics {
    DWORD dwTotalChecks;
    DWORD dwSuccessfulChecks;
    DWORD dwFailedChecks;
    DWORD dwFileChecks;
    DWORD dwMemoryChecks;
    DWORD dwSystemFileChecks;
    DWORD dwGameFileChecks;
    DWORD dwExecutableChecks;
    DWORD dwLibraryChecks;
    DWORD dwConfigurationChecks;
    DWORD dwChecksumMismatches;
    DWORD dwSignatureFailures;
    DWORD dwMemoryCorruptions;
    DWORD dwPatternMismatches;
    DWORD dwFilesRepaired;
    DWORD dwMemoryRepaired;
    DWORD dwAverageCheckTime;
    DWORD dwMaxCheckTime;
    DWORD dwLastCheckTime;
};

/*
 * CIntegrityManager - File and memory integrity management
 * Manages all integrity checking and validation systems
 * Enhanced with comprehensive security measures and RF Online compatibility
 */
class CIntegrityManager {
public:
    // Singleton access
    static CIntegrityManager& Instance();

    // Core lifecycle methods
    bool Initialize();
    void Shutdown();
    void Update();

    // File integrity methods
    IntegrityResult CheckFileIntegrity(const char* szFilePath);
    IntegrityResult CheckFileChecksum(const char* szFilePath, DWORD dwExpectedChecksum);
    IntegrityResult CheckFileSignature(const char* szFilePath);
    IntegrityResult CheckSystemFiles();
    IntegrityResult CheckGameFiles();
    IntegrityResult CheckExecutableFiles();
    IntegrityResult CheckLibraryFiles();
    IntegrityResult CheckConfigurationFiles();

    // Memory integrity methods
    IntegrityResult CheckMemoryIntegrity(void* pMemory, DWORD dwSize);
    IntegrityResult CheckMemoryChecksum(void* pMemory, DWORD dwSize, DWORD dwExpectedChecksum);
    IntegrityResult CheckMemoryPattern(void* pMemory, DWORD dwSize, DWORD dwExpectedPattern);
    IntegrityResult CheckExecutableMemory();
    IntegrityResult CheckLibraryMemory();

    // Batch integrity operations
    IntegrityResult CheckAllFiles();
    IntegrityResult CheckAllMemory();
    IntegrityResult CheckCriticalSystems();

    // File monitoring methods
    bool StartFileMonitoring(const char* szDirectory);
    bool StopFileMonitoring(const char* szDirectory);
    bool IsFileMonitored(const char* szFilePath);

    // Repair methods
    bool RepairFile(const char* szFilePath);
    bool RepairMemory(void* pMemory, DWORD dwSize);
    bool RestoreFromBackup(const char* szFilePath);

    // Utility methods
    DWORD CalculateFileChecksum(const char* szFilePath);
    DWORD CalculateMemoryChecksum(const void* pMemory, DWORD dwSize);
    bool ValidateFileSignature(const char* szFilePath);
    bool CreateFileBackup(const char* szFilePath);

    // Information retrieval
    bool GetFileIntegrityInfo(const char* szFilePath, FileIntegrityInfo& info);
    bool GetMemoryIntegrityInfo(void* pMemory, DWORD dwSize, MemoryIntegrityInfo& info);

    // Configuration and statistics
    const IntegrityConfig& GetConfig() const { return m_Config; }
    const IntegrityStatistics& GetStatistics() const { return m_Statistics; }
    void ResetStatistics();

    // Callback system
    bool RegisterIntegrityCallback(void (*callback)(IntegrityCheckType, IntegrityResult, const char*));
    void LogIntegrityEvent(IntegrityCheckType type, IntegrityResult result, const char* szDetails);

private:
    // Private constructor for singleton
    CIntegrityManager();
    ~CIntegrityManager();

    // Prevent copying
    CIntegrityManager(const CIntegrityManager&) = delete;
    CIntegrityManager& operator=(const CIntegrityManager&) = delete;

    // Internal methods
    bool LoadConfiguration();
    bool InitializeFileMonitoring();
    bool InitializeMemoryMonitoring();
    void ShutdownFileMonitoring();
    void ShutdownMemoryMonitoring();
    IntegrityResult ProcessIntegrityResult(IntegrityResult result, IntegrityCheckType type);
    void UpdateStatistics();

    // File system methods
    bool EnumerateFiles(const char* szDirectory, std::vector<std::string>& files);
    bool IsSystemFile(const char* szFilePath);
    bool IsGameFile(const char* szFilePath);
    bool IsExecutableFile(const char* szFilePath);
    bool IsLibraryFile(const char* szFilePath);
    bool IsConfigurationFile(const char* szFilePath);

    // Member variables
    static CIntegrityManager* s_pInstance;
    static std::mutex s_InstanceMutex;

    bool m_bInitialized;
    bool m_bFileMonitoringActive;
    bool m_bMemoryMonitoringActive;
    IntegrityConfig m_Config;
    IntegrityStatistics m_Statistics;

    // File integrity tracking
    std::map<std::string, FileIntegrityInfo> m_FileIntegrityMap;
    std::vector<std::string> m_MonitoredDirectories;
    std::vector<std::string> m_SystemFiles;
    std::vector<std::string> m_GameFiles;
    std::vector<std::string> m_ExecutableFiles;
    std::vector<std::string> m_LibraryFiles;
    std::vector<std::string> m_ConfigurationFiles;

    // Memory integrity tracking
    std::map<void*, MemoryIntegrityInfo> m_MemoryIntegrityMap;
    std::vector<void*> m_MonitoredMemoryRegions;

    // Synchronization
    mutable std::mutex m_ConfigMutex;
    mutable std::mutex m_StatisticsMutex;
    mutable std::mutex m_FileIntegrityMutex;
    mutable std::mutex m_MemoryIntegrityMutex;
    mutable std::mutex m_MonitoringMutex;

    // Callback system
    std::vector<void (*)(IntegrityCheckType, IntegrityResult, const char*)> m_IntegrityCallbacks;

    // Performance tracking
    DWORD m_dwLastUpdateTime;
    DWORD m_dwUpdateInterval;
    DWORD m_dwCheckStartTime;
    DWORD m_dwConcurrentChecks;
};
