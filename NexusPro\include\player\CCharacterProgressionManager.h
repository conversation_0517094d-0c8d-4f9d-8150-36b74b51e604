#pragma once

/**
 * @file CCharacterProgressionManager.h
 * @brief Character Progression and Experience Management System for NexusPro
 * 
 * Manages comprehensive character progression including experience calculation,
 * level advancement, PC bang bonuses, potion effects, recovery items, and
 * complex progression mechanics with multiple bonus systems.
 * 
 * Original decompiled functions:
 * - AlterExp: 0x14005BB50
 * - AlterExp_Potion: 0x14005C550
 * - CalcExp: 0x14005A600
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @category Player Extended Systems - Phase 2 Batch 8
 * @note STRICT ADHERENCE TO DECOMPILED SOURCE: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */

#include "../common/WindowsTypes.h"
#include "../common/RFProtocol.h"
#include "../common/Stubs.h"
#include "../database/CPlayerDB.h"
#include "../database/CUserDB.h"
#include "../static/cStaticMember_Player.h"
#include "../party/CPartyModeKillMonsterExpNotify.h"
#include <memory>
#include <mutex>
#include <cmath>

// Forward declarations
class CPlayer;
class CPlayerDB;
class CUserDB;
class CCharacter;
class cStaticMember_Player;
class CPartyModeKillMonsterExpNotify;

/**
 * @enum ExperienceType
 * @brief Types of experience modifications
 */
enum ExperienceType {
    EXP_COMBAT = 0,                                  ///< Combat experience
    EXP_QUEST = 1,                                   ///< Quest completion experience
    EXP_POTION = 2,                                  ///< Potion-based experience
    EXP_RECOVERY = 3,                                ///< Recovery item experience
    EXP_BONUS = 4,                                   ///< Bonus experience (PC bang, etc.)
    EXP_PENALTY = 5                                  ///< Experience penalty
};

/**
 * @enum ExperienceModifier
 * @brief Experience modification flags
 */
enum ExperienceModifier {
    MOD_NONE = 0x00,                                 ///< No modifiers
    MOD_REWARD = 0x01,                               ///< Reward-based experience
    MOD_RECOVER_ITEM = 0x02,                         ///< Using experience recovery item
    MOD_ADDITION_ITEM = 0x04,                        ///< Using experience addition item
    MOD_PC_BANG = 0x08,                              ///< PC bang bonus
    MOD_PARTY = 0x10,                                ///< Party experience sharing
    MOD_PREMIUM = 0x20                               ///< Premium account bonus
};

/**
 * @struct ExperienceCalculationData
 * @brief Data structure for experience calculations
 */
struct ExperienceCalculationData {
    long double dBaseExp;                            ///< Base experience amount
    long double dModifiedExp;                        ///< Modified experience after bonuses
    double dMultiplier;                              ///< Experience multiplier
    DWORD dwModifiers;                               ///< Active modifiers (ExperienceModifier flags)
    bool bLevelUp;                                   ///< Whether level up occurred
    BYTE byOldLevel;                                 ///< Level before experience change
    BYTE byNewLevel;                                 ///< Level after experience change
    
    /**
     * @brief Default constructor
     */
    ExperienceCalculationData();
};

/**
 * @class CCharacterProgressionManager
 * @brief Manages character progression, experience, and level advancement
 * 
 * This class handles all aspects of character progression including:
 * - Complex experience calculation with multiple bonus systems
 * - Level progression and maximum level validation
 * - PC bang bonus calculation and application
 * - Potion-based experience modifications
 * - Recovery item experience restoration
 * - Party experience sharing and distribution
 * - Experience rate calculation and database synchronization
 * 
 * Based on decompiled source at: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */
class CCharacterProgressionManager {
public:
    /**
     * @brief Default constructor
     * Initializes character progression manager with RAII resource management
     */
    CCharacterProgressionManager();

    /**
     * @brief Virtual destructor for proper cleanup
     * Ensures all progression operations are properly completed and resources released
     */
    virtual ~CCharacterProgressionManager();

    // === Core Experience Management ===
    
    /**
     * @brief Alters player experience with comprehensive bonus calculations
     * 
     * Performs complex experience modification including:
     * - Multiple bonus system integration (PC bang, recovery items, addition items)
     * - Level progression validation and calculation
     * - Experience rate updates and database synchronization
     * - Equipment level limit checking and notifications
     * - Debug memory initialization (0xCCCCCCCC pattern)
     * - Comprehensive parameter validation
     * 
     * @param pPlayer Player to alter experience for
     * @param dAlterExp Base experience change amount (positive for gain, negative for loss)
     * @param bReward Whether this is a reward-based experience gain
     * @param bUseExpRecoverItem Whether using experience recovery item
     * @param dMultiplier Experience multiplier factor
     * @param bUseExpAdditionItem Whether using experience addition item
     * 
     * Original address: 0x14005BB50
     * Function: ?AlterExp@CPlayer@@QEAAXN_N00@Z
     */
    void AlterExp(CPlayer *pPlayer, long double dAlterExp, bool bReward, bool bUseExpRecoverItem, 
                  double dMultiplier, bool bUseExpAdditionItem);

    /**
     * @brief Alters player experience from potion consumption
     * 
     * Handles experience changes from potion usage including level progression
     * calculations and database updates.
     * 
     * @param pPlayer Player consuming potion
     * @param dPotionExp Experience amount from potion
     * 
     * Original address: 0x14005C550
     * Function: ?AlterExp_Potion@CPlayer@@QEAAXN@Z
     */
    void AlterExp_Potion(CPlayer *pPlayer, long double dPotionExp);

    /**
     * @brief Calculates experience from combat encounters
     * 
     * Computes experience gained from combat including party sharing,
     * monster difficulty bonuses, and kill notifications.
     * 
     * @param pPlayer Player gaining experience
     * @param pTarget Combat target
     * @param nDamageContribution Damage contribution percentage
     * @param pPartyNotify Party experience notification handler
     * 
     * Original address: 0x14005A600
     * Function: ?CalcExp@CPlayer@@QEAAXPEAV1@HAEAV CPartyModeKillMonsterExpNotify@@@Z
     */
    void CalcExp(CPlayer *pPlayer, CCharacter *pTarget, int nDamageContribution, 
                 CPartyModeKillMonsterExpNotify &pPartyNotify);

    // === Level Management ===
    
    /**
     * @brief Processes level advancement
     * @param pPlayer Player advancing in level
     * @param byNewLevel New level achieved
     * @return true if level advancement was successful
     */
    bool ProcessLevelAdvancement(CPlayer *pPlayer, BYTE byNewLevel);

    /**
     * @brief Gets maximum level for player
     * @param pPlayer Player to check maximum level for
     * @return Maximum level allowed
     */
    BYTE GetMaxLevel(CPlayer *pPlayer) const;

    /**
     * @brief Calculates experience required for next level
     * @param pPlayer Player to calculate for
     * @param byLevel Target level
     * @return Experience required for specified level
     */
    unsigned __int64 GetRequiredExperienceForLevel(CPlayer *pPlayer, BYTE byLevel) const;

    /**
     * @brief Calculates current experience rate
     * @param pPlayer Player to calculate rate for
     * @return Experience rate as percentage (0-1000000)
     */
    DWORD CalculateExperienceRate(CPlayer *pPlayer) const;

    // === Bonus System Management ===
    
    /**
     * @brief Calculates PC bang experience bonus
     * @param pPlayer Player to calculate bonus for
     * @param dBaseExp Base experience amount
     * @return Experience amount with PC bang bonus applied
     */
    long double CalculatePcBangBonus(CPlayer *pPlayer, long double dBaseExp) const;

    /**
     * @brief Applies recovery item experience bonus
     * @param pPlayer Player using recovery item
     * @param dBaseExp Base experience amount
     * @return Experience amount with recovery bonus applied
     */
    long double ApplyRecoveryItemBonus(CPlayer *pPlayer, long double dBaseExp) const;

    /**
     * @brief Applies addition item experience bonus
     * @param pPlayer Player using addition item
     * @param dBaseExp Base experience amount
     * @return Experience amount with addition bonus applied
     */
    long double ApplyAdditionItemBonus(CPlayer *pPlayer, long double dBaseExp) const;

    /**
     * @brief Calculates party experience sharing
     * @param pPlayer Player in party
     * @param dTotalExp Total experience to share
     * @return Player's share of party experience
     */
    long double CalculatePartyExperienceShare(CPlayer *pPlayer, long double dTotalExp) const;

    // === Database Integration ===
    
    /**
     * @brief Updates player experience in database
     * @param pPlayer Player to update
     * @param dNewExp New experience value
     */
    void UpdatePlayerExperienceInDatabase(CPlayer *pPlayer, double dNewExp);

    /**
     * @brief Updates user experience in database
     * @param pPlayer Player to update
     * @param dNewExp New experience value
     */
    void UpdateUserExperienceInDatabase(CPlayer *pPlayer, double dNewExp);

    /**
     * @brief Synchronizes experience data across all systems
     * @param pPlayer Player to synchronize
     */
    void SynchronizeExperienceData(CPlayer *pPlayer);

    // === Validation and Utility ===
    
    /**
     * @brief Validates experience modification parameters
     * @param pPlayer Player to validate
     * @param dExpChange Experience change amount
     * @param dwModifiers Active modifiers
     * @return true if parameters are valid
     */
    bool ValidateExperienceParameters(CPlayer *pPlayer, long double dExpChange, DWORD dwModifiers) const;

    /**
     * @brief Checks if player can gain experience
     * @param pPlayer Player to check
     * @return true if player can gain experience
     */
    bool CanGainExperience(CPlayer *pPlayer) const;

    /**
     * @brief Gets experience multiplier for player
     * @param pPlayer Player to get multiplier for
     * @return Current experience multiplier
     */
    double GetExperienceMultiplier(CPlayer *pPlayer) const;

    // === Notifications and Messaging ===
    
    /**
     * @brief Sends experience alteration notification
     * @param pPlayer Player to notify
     */
    void SendMsg_AlterExpInform(CPlayer *pPlayer);

    /**
     * @brief Sends equipment level limit notification
     * @param pPlayer Player to notify
     * @param byNewLevel New level achieved
     */
    void SendMsg_EquipItemLevelLimit(CPlayer *pPlayer, BYTE byNewLevel);

    /**
     * @brief Sends level up notification
     * @param pPlayer Player who leveled up
     */
    void SendMsg_LevelUp(CPlayer *pPlayer);

protected:
    // === Core Configuration ===
    bool m_bInitialized;                             ///< Initialization status
    
    // === System References ===
    cStaticMember_Player* m_pStaticMember;           ///< Static member reference
    
    // === Configuration Constants ===
    static constexpr double BASE_EXP_MULTIPLIER = 1.0; ///< Base experience multiplier
    static constexpr double PC_BANG_BONUS = 1.2;     ///< PC bang bonus (20% increase)
    static constexpr double RECOVERY_ITEM_BONUS = 1.5; ///< Recovery item bonus (50% increase)
    static constexpr double ADDITION_ITEM_BONUS = 2.0; ///< Addition item bonus (100% increase)
    static constexpr double PARTY_EXP_BONUS = 1.1;   ///< Party experience bonus (10% increase)
    static constexpr DWORD EXP_RATE_MULTIPLIER = 1000000; ///< Experience rate calculation multiplier
    static constexpr long double MIN_EXP_CHANGE = 0.1; ///< Minimum experience change
    static constexpr long double MAX_EXP_CHANGE = 999999999.0; ///< Maximum experience change
    
    // === Security and Validation ===
    mutable std::mutex m_progressionMutex;           ///< Mutex for thread-safe operations

private:
    // === Helper Methods ===
    
    /**
     * @brief Performs debug memory initialization
     * 
     * Initializes memory with debug pattern (0xCCCCCCCC) as per
     * decompiled source implementation.
     */
    void InitializeDebugMemory();

    /**
     * @brief Calculates final experience with all bonuses
     * @param pPlayer Player to calculate for
     * @param dBaseExp Base experience amount
     * @param dwModifiers Active modifiers
     * @return Final experience amount with all bonuses applied
     */
    long double CalculateFinalExperience(CPlayer *pPlayer, long double dBaseExp, DWORD dwModifiers) const;

    /**
     * @brief Processes level up effects
     * @param pPlayer Player leveling up
     * @param byOldLevel Previous level
     * @param byNewLevel New level
     */
    void ProcessLevelUpEffects(CPlayer *pPlayer, BYTE byOldLevel, BYTE byNewLevel);

    /**
     * @brief Logs progression operation for audit
     * @param pPlayer Player performing operation
     * @param strOperation Operation description
     * @param dExpChange Experience change amount
     */
    void LogProgressionOperation(CPlayer* pPlayer, const char* strOperation, long double dExpChange) const;

    /**
     * @brief Validates player state for experience changes
     * @param pPlayer Player to validate
     * @return true if player is in valid state
     */
    bool ValidatePlayerStateForExperience(CPlayer *pPlayer) const;

    /**
     * @brief Updates equipment effects after level change
     * @param pPlayer Player to update equipment for
     */
    void UpdateEquipmentEffectsAfterLevelChange(CPlayer *pPlayer);

    /**
     * @brief Calculates experience overflow handling
     * @param pPlayer Player with experience overflow
     * @param dOverflowExp Overflow experience amount
     */
    void HandleExperienceOverflow(CPlayer *pPlayer, long double dOverflowExp);
};
