#pragma once

// Generated for NexusPro - RF Online Zone Server
// Class: CProtocolManager
// Category: system
// Description: Protocol handling and message processing system for RF Online
// Based on decompiled protocol patterns and message processing infrastructure
// EXACT RF Online compatibility with original memory addresses in comments

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"
#include "../common/common_headers.h"
#include <map>
#include <vector>
#include <queue>
#include <mutex>

// Forward declarations
class CPacketManager;
class CConnectionManager;
class CMessageManager;
class CEncryptionManager;
class CNetworkManager;

// Protocol types - Based on RF Online protocol patterns
enum ProtocolType {
    PROTOCOL_NONE = 0,
    PROTOCOL_LOGIN = 1,
    PROTOCOL_GAME = 2,
    PROTOCOL_CHAT = 3,
    PROTOCOL_GUILD = 4,
    PROTOCOL_TRADE = 5,
    PROTOCOL_COMBAT = 6,
    PROTOCOL_ITEM = 7,
    PROTOCOL_QUEST = 8,
    PROTOCOL_BILLING = 9,
    PROTOCOL_ADMIN = 10,
    PROTOCOL_SECURITY = 11,
    PROTOCOL_CASH = 12,
    PROTOCOL_SYSTEM = 13,
    PROTOCOL_DEBUG = 14,
    PROTOCOL_MAX = 15
};

// Protocol states - RF Online compatible
enum ProtocolState {
    PROTOCOL_STATE_IDLE = 0,
    PROTOCOL_STATE_PROCESSING = 1,
    PROTOCOL_STATE_WAITING = 2,
    PROTOCOL_STATE_COMPLETED = 3,
    PROTOCOL_STATE_ERROR = 4,
    PROTOCOL_STATE_TIMEOUT = 5,
    PROTOCOL_STATE_CANCELLED = 6,
    PROTOCOL_STATE_MAX = 7
};

// Message processing modes
enum MessageProcessMode {
    MSG_PROCESS_SYNC = 0,
    MSG_PROCESS_ASYNC = 1,
    MSG_PROCESS_QUEUED = 2,
    MSG_PROCESS_IMMEDIATE = 3,
    MSG_PROCESS_DEFERRED = 4,
    MSG_PROCESS_MAX = 5
};

// Protocol header structure - RF Online compatible
struct ProtocolHeader {
    DWORD dwSize;              // Total message size
    DWORD dwType;              // Protocol type
    DWORD dwSubType;           // Protocol subtype
    DWORD dwSequence;          // Sequence number
    DWORD dwChecksum;          // Message checksum
    DWORD dwTimestamp;         // Message timestamp
    DWORD dwSender;            // Sender identifier
    DWORD dwReceiver;          // Receiver identifier
    DWORD dwFlags;             // Protocol flags
    DWORD dwReserved;          // Reserved field
};

// Protocol message structure
struct ProtocolMessage {
    ProtocolHeader header;     // Message header
    BYTE data[8192];           // Message data
    DWORD dwActualSize;        // Actual data size
    DWORD dwCreateTime;        // Creation time
    DWORD dwProcessTime;       // Processing time
    ProtocolState state;       // Current state
    MessageProcessMode mode;   // Processing mode
    DWORD dwRetryCount;        // Retry counter
    DWORD dwPriority;          // Message priority
    void* pUserData;           // User data pointer
    
    // Constructor
    ProtocolMessage() {
        memset(&header, 0, sizeof(header));
        memset(data, 0, sizeof(data));
        dwActualSize = 0;
        dwCreateTime = 0;
        dwProcessTime = 0;
        state = PROTOCOL_STATE_IDLE;
        mode = MSG_PROCESS_SYNC;
        dwRetryCount = 0;
        dwPriority = 0;
        pUserData = nullptr;
    }
};

// Protocol handler function pointer
typedef bool (*ProtocolHandler)(const ProtocolMessage& message, void* pUserData);

// Protocol registration structure
struct ProtocolRegistration {
    ProtocolType type;         // Protocol type
    DWORD dwSubType;           // Protocol subtype
    ProtocolHandler handler;   // Handler function
    void* pUserData;           // User data for handler
    MessageProcessMode mode;   // Processing mode
    DWORD dwTimeout;           // Processing timeout
    bool bEnabled;             // Handler enabled
    DWORD dwCallCount;         // Number of calls
    DWORD dwSuccessCount;      // Successful calls
    DWORD dwErrorCount;        // Error count
    DWORD dwLastCallTime;      // Last call time
};

// Protocol statistics structure
struct ProtocolStats {
    DWORD dwTotalMessages;     // Total messages processed
    DWORD dwSuccessfulMessages; // Successfully processed
    DWORD dwFailedMessages;    // Failed messages
    DWORD dwTimeoutMessages;   // Timeout messages
    DWORD dwAverageProcessTime; // Average processing time
    DWORD dwPeakProcessTime;   // Peak processing time
    DWORD dwTotalHandlers;     // Total registered handlers
    DWORD dwActiveHandlers;    // Active handlers
    DWORD dwLastUpdateTime;    // Last statistics update
    DWORD dwMessageRate;       // Messages per second
};

// Protocol configuration structure
struct ProtocolConfig {
    DWORD dwMaxMessageSize;    // Maximum message size
    DWORD dwMaxQueueSize;      // Maximum queue size
    DWORD dwProcessTimeout;    // Processing timeout
    DWORD dwRetryLimit;        // Retry limit
    bool bEnableLogging;       // Enable protocol logging
    bool bEnableValidation;    // Enable message validation
    bool bEnableEncryption;    // Enable message encryption
    bool bEnableCompression;   // Enable message compression
    DWORD dwThreadPoolSize;    // Thread pool size
    DWORD dwHeartbeatInterval; // Heartbeat interval
};

/**
 * @class CProtocolManager
 * @brief Protocol handling and message processing system for RF Online Zone Server
 * @details Manages protocol registration, message routing, and processing with RF Online compatibility
 *          Following exact decompiled source patterns from protocol processing infrastructure
 */
class CProtocolManager {
public:
    // Singleton pattern methods
    static CProtocolManager* GetInstance();
    static void DestroyInstance();

    // Core lifecycle methods
    bool Initialize();
    void Shutdown();
    
    // Protocol registration and management
    bool RegisterProtocolHandler(ProtocolType type, DWORD dwSubType, ProtocolHandler handler, void* pUserData = nullptr, MessageProcessMode mode = MSG_PROCESS_SYNC);
    bool UnregisterProtocolHandler(ProtocolType type, DWORD dwSubType);
    bool IsProtocolRegistered(ProtocolType type, DWORD dwSubType) const;
    ProtocolRegistration* GetProtocolRegistration(ProtocolType type, DWORD dwSubType);
    
    // Message processing methods
    bool ProcessMessage(const ProtocolMessage& message);
    bool ProcessMessageAsync(const ProtocolMessage& message);
    bool QueueMessage(const ProtocolMessage& message);
    bool ProcessQueuedMessages();
    void FlushMessageQueue();
    
    // Message creation and manipulation
    bool CreateMessage(ProtocolType type, DWORD dwSubType, const void* pData, DWORD dwSize, ProtocolMessage& message);
    bool CreateResponseMessage(const ProtocolMessage& requestMessage, DWORD dwResponseCode, const void* pData, DWORD dwSize, ProtocolMessage& responseMessage);
    bool CloneMessage(const ProtocolMessage& source, ProtocolMessage& destination);
    bool ValidateMessage(const ProtocolMessage& message);
    
    // Protocol routing and filtering
    bool RouteMessage(const ProtocolMessage& message);
    bool FilterMessage(const ProtocolMessage& message);
    bool AuthorizeMessage(DWORD dwSender, const ProtocolMessage& message);
    bool PrioritizeMessage(ProtocolMessage& message);
    
    // Message serialization and deserialization
    bool SerializeMessage(const ProtocolMessage& message, void* pBuffer, DWORD dwBufferSize, DWORD* pdwSerializedSize);
    bool DeserializeMessage(const void* pBuffer, DWORD dwBufferSize, ProtocolMessage& message);
    bool CompressMessage(ProtocolMessage& message);
    bool DecompressMessage(ProtocolMessage& message);
    
    // Protocol security and validation
    bool EncryptMessage(ProtocolMessage& message);
    bool DecryptMessage(ProtocolMessage& message);
    bool SignMessage(ProtocolMessage& message);
    bool VerifyMessageSignature(const ProtocolMessage& message);
    bool CalculateMessageChecksum(ProtocolMessage& message);
    bool VerifyMessageChecksum(const ProtocolMessage& message);
    
    // Protocol monitoring and statistics
    const ProtocolStats& GetProtocolStats() const { return m_Stats; }
    void ResetProtocolStats();
    void UpdateProtocolStats(const ProtocolMessage& message, bool bSuccess, DWORD dwProcessTime);
    DWORD GetQueueSize() const;
    DWORD GetActiveHandlerCount() const;
    
    // Protocol configuration and settings
    bool LoadProtocolConfig(const char* szConfigFile);
    bool SaveProtocolConfig(const char* szConfigFile);
    void SetMaxMessageSize(DWORD dwMaxSize);
    void SetProcessTimeout(DWORD dwTimeout);
    void SetRetryLimit(DWORD dwRetryLimit);
    void EnableProtocolLogging(bool bEnabled);
    void EnableMessageValidation(bool bEnabled);
    void EnableMessageEncryption(bool bEnabled);
    void EnableMessageCompression(bool bEnabled);
    
    // Protocol type handlers - RF Online specific
    bool ProcessLoginProtocol(const ProtocolMessage& message);
    bool ProcessGameProtocol(const ProtocolMessage& message);
    bool ProcessChatProtocol(const ProtocolMessage& message);
    bool ProcessGuildProtocol(const ProtocolMessage& message);
    bool ProcessTradeProtocol(const ProtocolMessage& message);
    bool ProcessCombatProtocol(const ProtocolMessage& message);
    bool ProcessItemProtocol(const ProtocolMessage& message);
    bool ProcessQuestProtocol(const ProtocolMessage& message);
    bool ProcessBillingProtocol(const ProtocolMessage& message);
    bool ProcessAdminProtocol(const ProtocolMessage& message);
    bool ProcessSecurityProtocol(const ProtocolMessage& message);
    bool ProcessCashProtocol(const ProtocolMessage& message);
    bool ProcessSystemProtocol(const ProtocolMessage& message);
    bool ProcessDebugProtocol(const ProtocolMessage& message);
    
    // Protocol utilities
    const char* GetProtocolTypeName(ProtocolType type) const;
    const char* GetProtocolStateName(ProtocolState state) const;
    const char* GetProcessModeName(MessageProcessMode mode) const;
    bool IsValidProtocolType(ProtocolType type) const;
    bool IsValidProtocolState(ProtocolState state) const;
    
    // Status and information methods
    bool IsInitialized() const { return m_bInitialized; }
    const char* GetLastError() const { return m_szLastError; }
    void ClearLastError() { m_szLastError[0] = '\0'; }
    
    // Debug and diagnostics
    void DumpProtocolInfo();
    void DumpHandlerInfo();
    void DumpMessageInfo(const ProtocolMessage& message);
    void DumpQueueStatus();
    bool EnableProtocolDebugging(bool bEnabled);
    bool IsProtocolDebuggingEnabled() const { return m_bProtocolDebugging; }

private:
    // Private constructor/destructor for singleton
    CProtocolManager();
    ~CProtocolManager();
    
    // Singleton instance management
    static CProtocolManager* m_pInstance;
    static std::mutex m_instanceMutex;
    
    // Core member variables
    bool m_bInitialized;
    bool m_bShuttingDown;
    DWORD m_dwStartTime;
    DWORD m_dwLastUpdateTime;
    
    // Protocol handler registry
    std::map<std::pair<ProtocolType, DWORD>, ProtocolRegistration> m_ProtocolHandlers;
    
    // Message processing queues
    std::queue<ProtocolMessage> m_MessageQueue;
    std::queue<ProtocolMessage> m_PriorityQueue;
    std::queue<ProtocolMessage> m_AsyncQueue;
    
    // Statistics and monitoring
    ProtocolStats m_Stats;
    
    // Configuration settings
    ProtocolConfig m_Config;
    bool m_bProtocolDebugging;
    
    // Thread safety
    mutable std::mutex m_handlerMutex;
    mutable std::mutex m_queueMutex;
    mutable std::mutex m_statsMutex;
    mutable std::mutex m_configMutex;
    
    // Error handling
    char m_szLastError[512];
    
    // Internal helper methods
    bool ValidateProtocolType(ProtocolType type);
    bool ValidateProtocolState(ProtocolState state);
    bool ValidateMessageProcessMode(MessageProcessMode mode);
    void SetLastError(const char* szFormat, ...);
    
    // Message processing helpers
    bool InternalProcessMessage(const ProtocolMessage& message, ProtocolRegistration* pRegistration);
    bool PreprocessMessage(ProtocolMessage& message);
    bool PostprocessMessage(ProtocolMessage& message);
    void UpdateMessageState(ProtocolMessage& message, ProtocolState newState);
    
    // Handler management helpers
    std::pair<ProtocolType, DWORD> MakeHandlerKey(ProtocolType type, DWORD dwSubType);
    bool IsHandlerEnabled(const ProtocolRegistration& registration);
    void UpdateHandlerStats(ProtocolRegistration& registration, bool bSuccess, DWORD dwProcessTime);
    
    // Queue management helpers
    bool IsQueueFull() const;
    void TrimQueue(std::queue<ProtocolMessage>& queue);
    ProtocolMessage* GetNextQueuedMessage();
    void ReorderQueue(std::queue<ProtocolMessage>& queue);
    
    // Security helpers
    bool ValidateMessageSecurity(const ProtocolMessage& message);
    bool CheckMessageIntegrity(const ProtocolMessage& message);
    bool ValidateMessageTimestamp(const ProtocolMessage& message);
    bool CheckMessageSize(const ProtocolMessage& message);
    
    // Configuration helpers
    bool ParseProtocolConfig(const char* szConfigData);
    bool WriteProtocolConfig(char* szConfigData, DWORD dwSize);
    void ApplyDefaultConfig();
    
    // Default protocol handlers
    void RegisterDefaultHandlers();
    void UnregisterAllHandlers();
};

// Global access macros for convenience
#define g_pProtocolManager CProtocolManager::GetInstance()

// Protocol processing helper macros - RF Online compatible
#define RF_PROCESS_PROTOCOL(message) \
    g_pProtocolManager->ProcessMessage(message)
#define RF_QUEUE_PROTOCOL(message) \
    g_pProtocolManager->QueueMessage(message)
#define RF_REGISTER_HANDLER(type, subType, handler, userData) \
    g_pProtocolManager->RegisterProtocolHandler(type, subType, handler, userData)

// Message creation helper macros
#define RF_CREATE_PROTOCOL_MESSAGE(type, subType, data, size, message) \
    g_pProtocolManager->CreateMessage(type, subType, data, size, message)
#define RF_CREATE_RESPONSE_MESSAGE(request, responseCode, data, size, response) \
    g_pProtocolManager->CreateResponseMessage(request, responseCode, data, size, response)

// Protocol validation helper macros
#define RF_VALIDATE_PROTOCOL_MESSAGE(message) \
    g_pProtocolManager->ValidateMessage(message)
#define RF_AUTHORIZE_PROTOCOL_MESSAGE(sender, message) \
    g_pProtocolManager->AuthorizeMessage(sender, message)
