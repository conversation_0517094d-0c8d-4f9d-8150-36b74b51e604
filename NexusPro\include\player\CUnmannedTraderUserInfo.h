#pragma once

/**
 * @file CUnmannedTraderUserInfo.h
 * @brief Unmanned Trader User Information Management System for NexusPro
 * 
 * Manages unmanned trader operations, including buy/sell transactions,
 * user information tracking, and automated trading functionality.
 * Handles trader registration, item management, and transaction processing.
 * 
 * Original decompiled functions:
 * - BuyComplete: 0x1403568C0
 * - CheckBuy: 0x14035B7E0
 * - CheckBuyComplete: 0x140356460
 * - CheckSellComplete: 0x1403564E0
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @category Player Extended Systems - Phase 2 Batch 6
 * @note STRICT ADHERENCE TO DECOMPILED SOURCE: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */

#include "../common/WindowsTypes.h"
#include "../common/RFProtocol.h"
#include "../common/Stubs.h"
#include "../database/_STORAGE_LIST.h"
#include "../database/_INVENKEY.h"
#include "../common/CLogFile.h"
#include <memory>
#include <mutex>

// Forward declarations
class CPlayer;
class CPlayerDB;
class CMgrAvatorItemHistory;
struct _unmanned_trader_regist_item;

/**
 * @class CUnmannedTraderUserInfo
 * @brief Manages unmanned trader user information and transactions
 * 
 * This class handles all aspects of unmanned trader functionality including:
 * - User registration and information management
 * - Buy/sell transaction processing
 * - Item inventory management for traders
 * - Transaction logging and history tracking
 * - Security validation and fraud prevention
 * 
 * Based on decompiled source at: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */
class CUnmannedTraderUserInfo {
public:
    /**
     * @brief Default constructor
     * Initializes unmanned trader user info with RAII resource management
     */
    CUnmannedTraderUserInfo();

    /**
     * @brief Virtual destructor for proper cleanup
     * Ensures all trader operations are properly completed and resources released
     */
    virtual ~CUnmannedTraderUserInfo();

    // === Core Transaction Operations ===
    
    /**
     * @brief Completes buy transaction for unmanned trader
     * 
     * Processes the completion of a buy transaction, including item transfer,
     * payment processing, and transaction logging. Uses debug memory initialization
     * pattern (0xCCCCCCCC) as per decompiled source.
     * 
     * @param pkBuyer Player purchasing the item
     * @param dwSellerSerial Seller's serial number
     * @param wszSellerName Seller's name (wide string)
     * @param szSellerAccountName Seller's account name
     * @param dwRegistSerial Registration serial number
     * @param dwK Item key parameter
     * @param dwD Item durability
     * @param dwU Item level/upgrade
     * @param dwPrice Transaction price
     * @param lnUID Unique item identifier
     * @param pkLogger Log file for transaction recording
     * @param wAddItemSerial Output parameter for new item serial
     * @return 0 on success, negative error code on failure
     * 
     * Original address: 0x1403568C0
     * Function: ?BuyComplete@CUnmannedTraderUserInfo@@QEAAEPEAVCPlayer@@KPEAD1KK_KKK2PEAVCLogFile@@AEAG@Z
     */
    char BuyComplete(CPlayer *pkBuyer, unsigned int dwSellerSerial, char *wszSellerName, 
                     char *szSellerAccountName, unsigned int dwRegistSerial, unsigned int dwK, 
                     unsigned __int64 dwD, unsigned int dwU, unsigned int dwPrice, 
                     unsigned __int64 lnUID, CLogFile *pkLogger, unsigned __int16 *wAddItemSerial);

    /**
     * @brief Checks buy transaction validity
     * 
     * Validates buy transaction parameters and conditions before processing.
     * 
     * @param pRegistItem Registration item data
     * @return Status code of validation
     * 
     * Original address: 0x14035B7E0
     * Function: ?CheckBuy@CUnmannedTraderUserInfo@@AEAAEPEAU_unmanned_trader_regist_item@@@Z
     */
    char CheckBuy(const _unmanned_trader_regist_item *pRegistItem);

    /**
     * @brief Checks buy completion status
     * 
     * Verifies that buy transaction completed successfully and all
     * conditions are met.
     * 
     * @param pkBuyer Player who completed the purchase
     * @return Status code of completion check
     * 
     * Original address: 0x140356460
     * Function: ?CheckBuyComplete@CUnmannedTraderUserInfo@@QEAAEPEAVCPlayer@@@Z
     */
    char CheckBuyComplete(CPlayer *pkBuyer);

    /**
     * @brief Checks sell completion status
     * 
     * Verifies that sell transaction completed successfully and all
     * conditions are met.
     * 
     * @param pkSeller Player who completed the sale
     * @return Status code of completion check
     * 
     * Original address: 0x1403564E0
     * Function: ?CheckSellComplete@CUnmannedTraderUserInfo@@QEAAEPEAVCPlayer@@@Z
     */
    char CheckSellComplete(CPlayer *pkSeller);

    // === User Information Management ===
    
    /**
     * @brief Gets user serial number
     * @return User serial identifier
     */
    DWORD GetUserSerial() const { return m_dwUserSerial; }

    /**
     * @brief Sets user serial number
     * @param dwSerial New user serial
     */
    void SetUserSerial(DWORD dwSerial) { m_dwUserSerial = dwSerial; }

    /**
     * @brief Validates user permissions for trading
     * @param pPlayer Player to validate
     * @return true if player has trading permissions
     */
    bool ValidateUserPermissions(CPlayer* pPlayer) const;

    /**
     * @brief Gets trader registration status
     * @return true if trader is registered and active
     */
    bool IsTraderActive() const;

    /**
     * @brief Registers new trader
     * @param pPlayer Player registering as trader
     * @return true if registration successful
     */
    bool RegisterTrader(CPlayer* pPlayer);

    /**
     * @brief Unregisters trader
     * @param pPlayer Player unregistering
     * @return true if unregistration successful
     */
    bool UnregisterTrader(CPlayer* pPlayer);

    // === Transaction History ===
    
    /**
     * @brief Gets transaction count for user
     * @return Number of completed transactions
     */
    DWORD GetTransactionCount() const;

    /**
     * @brief Logs transaction for audit trail
     * @param strOperation Operation description
     * @param dwAmount Transaction amount
     */
    void LogTransaction(const char* strOperation, DWORD dwAmount);

private:
    // === Core User Data ===
    DWORD m_dwUserSerial;                            ///< User serial identifier
    bool m_bActive;                                  ///< Trader active status
    DWORD m_dwTransactionCount;                      ///< Number of transactions
    
    // === Security and Validation ===
    mutable std::mutex m_traderMutex;                ///< Mutex for thread-safe operations
    DWORD m_dwLastTransaction;                       ///< Last transaction timestamp
    
    // === Configuration ===
    static constexpr DWORD MAX_TRANSACTIONS = 1000;  ///< Maximum transactions per trader
    static constexpr DWORD TRANSACTION_TIMEOUT = 300000; ///< Transaction timeout (5 minutes)
    
    // === Helper Methods ===
    
    /**
     * @brief Validates transaction parameters
     * @param dwPrice Transaction price
     * @param dwItemKey Item key
     * @return true if parameters are valid
     */
    bool ValidateTransactionParams(DWORD dwPrice, DWORD dwItemKey) const;

    /**
     * @brief Processes item transfer
     * @param pBuyer Buyer player
     * @param pItemData Item data to transfer
     * @return true if transfer successful
     */
    bool ProcessItemTransfer(CPlayer* pBuyer, const _STORAGE_LIST::_db_con* pItemData);

    /**
     * @brief Processes payment
     * @param pBuyer Buyer player
     * @param dwAmount Payment amount
     * @return true if payment successful
     */
    bool ProcessPayment(CPlayer* pBuyer, DWORD dwAmount);

    /**
     * @brief Converts wide string to multibyte for logging
     * @param wszSource Wide string source
     * @param szDest Destination buffer
     * @param nMaxLen Maximum length
     */
    void W2M(const char* wszSource, char* szDest, unsigned int nMaxLen) const;
};

/**
 * @struct _unmanned_trader_regist_item
 * @brief Registration item data for unmanned trader
 */
struct _unmanned_trader_regist_item {
    DWORD dwItemCode;                                ///< Item code
    DWORD dwPrice;                                   ///< Item price
    DWORD dwQuantity;                                ///< Item quantity
    unsigned __int64 dwDurability;                   ///< Item durability
    DWORD dwLevel;                                   ///< Item level/upgrade
    unsigned __int64 lnUID;                          ///< Unique item identifier
    
    /**
     * @brief Default constructor
     */
    _unmanned_trader_regist_item();
};
