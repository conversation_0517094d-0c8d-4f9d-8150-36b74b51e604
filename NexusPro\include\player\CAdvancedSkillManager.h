#pragma once

/**
 * @file CAdvancedSkillManager.h
 * @brief Advanced Skill Management System for NexusPro
 * 
 * Manages advanced skill processing including skill validation, effect application,
 * consumption item management, skill level progression, aura processing, and
 * complex skill interactions with guild systems and character mechanics.
 * 
 * Original decompiled functions:
 * - skill_process: 0x14009B750
 * - skill_process_for_aura: 0x14009C450
 * - skill_process_for_item: 0x14009C430
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @category Player Extended Systems - Phase 2 Batch 8
 * @note STRICT ADHERENCE TO DECOMPILED SOURCE: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */

#include "../common/WindowsTypes.h"
#include "../common/RFProtocol.h"
#include "../common/Stubs.h"
#include "../database/_CHRID.h"
#include "../database/_skill_fld.h"
#include "../storage/_STORAGE_LIST.h"
#include "../guild/CGuildRoomSystem.h"
#include "../database/CUserDB.h"
#include <memory>
#include <mutex>
#include <vector>
#include <unordered_map>

// Forward declarations
class CPlayer;
class CCharacter;
class CUserDB;
class CGuild;
class CGuildRoomSystem;
struct _CHRID;
struct _skill_fld;

/**
 * @enum SkillProcessResult
 * @brief Result codes for skill processing
 */
enum SkillProcessResult {
    SKILL_SUCCESS = 0,                               ///< Skill processed successfully
    SKILL_INVALID_TARGET = 1,                        ///< Invalid target for skill
    SKILL_INSUFFICIENT_RESOURCES = 2,                ///< Insufficient resources (SP, items, etc.)
    SKILL_COOLDOWN_ACTIVE = 3,                       ///< Skill is on cooldown
    SKILL_INVALID_LEVEL = 4,                         ///< Invalid skill level
    SKILL_RANGE_ERROR = 5,                           ///< Target out of range
    SKILL_PERMISSION_DENIED = 6,                     ///< Permission denied for skill use
    SKILL_SYSTEM_ERROR = 7                           ///< System error during processing
};

/**
 * @enum SkillType
 * @brief Types of skills
 */
enum SkillType {
    SKILL_ATTACK = 0,                                ///< Attack skill
    SKILL_DEFENSE = 1,                               ///< Defense skill
    SKILL_SUPPORT = 2,                               ///< Support skill
    SKILL_AURA = 3,                                  ///< Aura skill
    SKILL_ITEM_BASED = 4,                            ///< Item-based skill
    SKILL_GUILD = 5,                                 ///< Guild skill
    SKILL_SPECIAL = 6                                ///< Special skill
};

/**
 * @struct SkillProcessData
 * @brief Data structure for skill processing
 */
struct SkillProcessData {
    int nEffectCode;                                 ///< Effect code for skill
    int nSkillIndex;                                 ///< Skill index
    int nSkillLevel;                                 ///< Skill level
    int nClassGrade;                                 ///< Class grade requirement
    _CHRID *pidDst;                                  ///< Target character ID
    CCharacter *pDst;                                ///< Target character pointer
    _skill_fld *pSkillFld;                           ///< Skill field data
    unsigned __int16 *pConsumeSerial;                ///< Consumption item serial
    int *pnLv;                                       ///< Level parameter
    float fEffectValue;                              ///< Effect value parameter
    
    /**
     * @brief Default constructor
     */
    SkillProcessData();
    
    /**
     * @brief Constructor with parameters
     */
    SkillProcessData(int effectCode, int skillIndex, _CHRID *target);
};

/**
 * @struct SkillConsumptionData
 * @brief Data for skill consumption requirements
 */
struct SkillConsumptionData {
    _STORAGE_LIST::_db_con *ppConsumeItems;          ///< Consumption items array
    int *pnConsume;                                  ///< Consumption amounts
    bool *pbOverLap;                                 ///< Overlap flags
    bool *pbUpMty;                                   ///< Empty flags
    unsigned __int16 pwDelPoint;                     ///< Deletion points
    bool bValidConsumption;                          ///< Consumption validation status
    
    /**
     * @brief Default constructor
     */
    SkillConsumptionData();
};

/**
 * @class CAdvancedSkillManager
 * @brief Manages advanced skill processing and interactions
 * 
 * This class handles all aspects of advanced skill functionality including:
 * - Complex skill processing with effect validation
 * - Consumption item management and validation
 * - Skill level progression and class grade requirements
 * - Aura skill processing and area effects
 * - Item-based skill interactions
 * - Guild skill integration and room system effects
 * - Multi-target skill processing and validation
 * 
 * Based on decompiled source at: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */
class CAdvancedSkillManager {
public:
    /**
     * @brief Default constructor
     * Initializes advanced skill manager with RAII resource management
     */
    CAdvancedSkillManager();

    /**
     * @brief Virtual destructor for proper cleanup
     * Ensures all skill operations are properly completed and resources released
     */
    virtual ~CAdvancedSkillManager();

    // === Core Skill Processing ===
    
    /**
     * @brief Processes skill with comprehensive validation and effects
     * 
     * Handles complex skill processing including:
     * - Target validation and character retrieval
     * - Skill field data validation and level checking
     * - Class grade requirement verification
     * - Consumption item processing and validation
     * - Guild room system integration
     * - Effect application and parameter processing
     * - Debug memory initialization (0xCCCCCCCC pattern)
     * 
     * @param pPlayer Player casting the skill
     * @param nEffectCode Effect code for the skill
     * @param nSkillIndex Index of the skill being cast
     * @param pidDst Target character ID
     * @param fEffectValue Effect value parameter
     * @param pConsumeSerial Consumption item serial output
     * @param pnLv Level parameter output
     * @return Skill processing result code
     * 
     * Original address: 0x14009B750
     * Function: ?skill_process@CPlayer@@QEAAEHHPEAU_CHRID@@PEAGPEAH@Z
     */
    char skill_process(CPlayer *pPlayer, int nEffectCode, int nSkillIndex, _CHRID *pidDst, 
                       float fEffectValue, unsigned __int16 *pConsumeSerial, int *pnLv);

    /**
     * @brief Processes aura skills with area effects
     * 
     * Handles aura skill processing including area validation and effect application.
     * 
     * @param pPlayer Player casting the aura skill
     * @param nSkillIndex Index of the aura skill
     * @return Aura processing result
     * 
     * Original address: 0x14009C450
     * Function: ?skill_process_for_aura@CPlayer@@QEAAXH@Z
     */
    void skill_process_for_aura(CPlayer *pPlayer, int nSkillIndex);

    /**
     * @brief Processes item-based skills
     * 
     * Handles skills that require specific items for activation.
     * 
     * @param pPlayer Player using the item skill
     * @param nSkillIndex Index of the item skill
     * @param pidDst Target character ID
     * @param pnLv Level parameter
     * @return Item skill processing result
     * 
     * Original address: 0x14009C430
     * Function: ?skill_process_for_item@CPlayer@@QEAAEHHPEAU_CHRID@@PEAH@Z
     */
    char skill_process_for_item(CPlayer *pPlayer, int nSkillIndex, _CHRID *pidDst, int *pnLv);

    // === Skill Validation ===
    
    /**
     * @brief Validates skill prerequisites
     * @param pPlayer Player attempting to use skill
     * @param skillData Skill processing data
     * @return true if prerequisites are met
     */
    bool ValidateSkillPrerequisites(CPlayer *pPlayer, const SkillProcessData &skillData) const;

    /**
     * @brief Validates target for skill
     * @param pPlayer Player casting skill
     * @param pTarget Target character
     * @param skillType Type of skill being cast
     * @return true if target is valid
     */
    bool ValidateSkillTarget(CPlayer *pPlayer, CCharacter *pTarget, SkillType skillType) const;

    /**
     * @brief Validates skill level requirements
     * @param pPlayer Player casting skill
     * @param nSkillIndex Skill index
     * @param nRequiredLevel Required skill level
     * @return true if level requirements are met
     */
    bool ValidateSkillLevel(CPlayer *pPlayer, int nSkillIndex, int nRequiredLevel) const;

    /**
     * @brief Validates class grade requirements
     * @param pPlayer Player casting skill
     * @param nRequiredGrade Required class grade
     * @return true if class grade requirements are met
     */
    bool ValidateClassGrade(CPlayer *pPlayer, int nRequiredGrade) const;

    // === Consumption Management ===
    
    /**
     * @brief Processes skill consumption requirements
     * @param pPlayer Player casting skill
     * @param skillData Skill processing data
     * @param consumptionData Consumption data to populate
     * @return true if consumption processing was successful
     */
    bool ProcessSkillConsumption(CPlayer *pPlayer, const SkillProcessData &skillData, 
                                 SkillConsumptionData &consumptionData);

    /**
     * @brief Validates consumption items
     * @param pPlayer Player to validate items for
     * @param consumptionData Consumption data to validate
     * @return true if consumption items are valid
     */
    bool ValidateConsumptionItems(CPlayer *pPlayer, const SkillConsumptionData &consumptionData) const;

    /**
     * @brief Consumes required items for skill
     * @param pPlayer Player to consume items from
     * @param consumptionData Consumption data
     * @return true if items were successfully consumed
     */
    bool ConsumeSkillItems(CPlayer *pPlayer, const SkillConsumptionData &consumptionData);

    // === Guild Integration ===
    
    /**
     * @brief Processes guild room system effects
     * @param pPlayer Player in guild room
     * @param pGuildRoomSystem Guild room system reference
     * @param skillData Skill processing data
     * @return Guild room processing result
     */
    bool ProcessGuildRoomEffects(CPlayer *pPlayer, CGuildRoomSystem *pGuildRoomSystem, 
                                 const SkillProcessData &skillData);

    /**
     * @brief Validates guild skill permissions
     * @param pPlayer Player attempting guild skill
     * @param pGuild Guild reference
     * @param nSkillIndex Guild skill index
     * @return true if player has guild skill permissions
     */
    bool ValidateGuildSkillPermissions(CPlayer *pPlayer, CGuild *pGuild, int nSkillIndex) const;

    // === Effect Application ===
    
    /**
     * @brief Applies skill effects to target
     * @param pCaster Player casting the skill
     * @param pTarget Target character
     * @param skillData Skill processing data
     * @return true if effects were applied successfully
     */
    bool ApplySkillEffects(CPlayer *pCaster, CCharacter *pTarget, const SkillProcessData &skillData);

    /**
     * @brief Applies aura effects to area
     * @param pCaster Player casting aura
     * @param nSkillIndex Aura skill index
     * @param fRadius Effect radius
     * @return Number of targets affected
     */
    int ApplyAuraEffects(CPlayer *pCaster, int nSkillIndex, float fRadius);

    /**
     * @brief Calculates skill damage
     * @param pCaster Player casting skill
     * @param pTarget Target character
     * @param skillData Skill data
     * @return Calculated damage amount
     */
    int CalculateSkillDamage(CPlayer *pCaster, CCharacter *pTarget, const SkillProcessData &skillData) const;

    /**
     * @brief Calculates skill healing
     * @param pCaster Player casting skill
     * @param pTarget Target character
     * @param skillData Skill data
     * @return Calculated healing amount
     */
    int CalculateSkillHealing(CPlayer *pCaster, CCharacter *pTarget, const SkillProcessData &skillData) const;

    // === Skill Data Management ===
    
    /**
     * @brief Gets skill field data
     * @param nSkillIndex Skill index
     * @return Pointer to skill field data, nullptr if not found
     */
    _skill_fld* GetSkillFieldData(int nSkillIndex) const;

    /**
     * @brief Gets skill type
     * @param nSkillIndex Skill index
     * @return Type of the skill
     */
    SkillType GetSkillType(int nSkillIndex) const;

    /**
     * @brief Gets skill cooldown
     * @param pPlayer Player to check cooldown for
     * @param nSkillIndex Skill index
     * @return Remaining cooldown in milliseconds
     */
    DWORD GetSkillCooldown(CPlayer *pPlayer, int nSkillIndex) const;

    /**
     * @brief Sets skill cooldown
     * @param pPlayer Player to set cooldown for
     * @param nSkillIndex Skill index
     * @param dwCooldown Cooldown duration in milliseconds
     */
    void SetSkillCooldown(CPlayer *pPlayer, int nSkillIndex, DWORD dwCooldown);

protected:
    // === Core Configuration ===
    bool m_bInitialized;                             ///< Initialization status
    
    // === Skill Data Cache ===
    std::unordered_map<int, _skill_fld*> m_skillCache; ///< Skill field data cache
    std::unordered_map<int, SkillType> m_skillTypes; ///< Skill type mapping
    
    // === Configuration Constants ===
    static constexpr int MAX_SKILL_LEVEL = 255;      ///< Maximum skill level
    static constexpr int MAX_CLASS_GRADE = 10;       ///< Maximum class grade
    static constexpr float DEFAULT_AURA_RADIUS = 10.0f; ///< Default aura radius
    static constexpr DWORD DEFAULT_SKILL_COOLDOWN = 1000; ///< Default skill cooldown (1 second)
    static constexpr int MAX_CONSUMPTION_ITEMS = 10; ///< Maximum consumption items per skill
    
    // === Security and Validation ===
    mutable std::mutex m_skillMutex;                 ///< Mutex for thread-safe operations

private:
    // === Helper Methods ===
    
    /**
     * @brief Performs debug memory initialization
     * 
     * Initializes memory with debug pattern (0xCCCCCCCC) as per
     * decompiled source implementation.
     */
    void InitializeDebugMemory();

    /**
     * @brief Validates skill processing parameters
     * @param pPlayer Player to validate
     * @param skillData Skill data to validate
     * @return true if parameters are valid
     */
    bool ValidateSkillParameters(CPlayer *pPlayer, const SkillProcessData &skillData) const;

    /**
     * @brief Processes skill level up data
     * @param pPlayer Player leveling up skill
     * @param nSkillIndex Skill index
     * @param nNewLevel New skill level
     */
    void ProcessSkillLevelUp(CPlayer *pPlayer, int nSkillIndex, int nNewLevel);

    /**
     * @brief Logs skill operation for audit
     * @param pPlayer Player performing operation
     * @param strOperation Operation description
     * @param nSkillIndex Optional skill index
     */
    void LogSkillOperation(CPlayer* pPlayer, const char* strOperation, int nSkillIndex = -1) const;

    /**
     * @brief Validates player state for skill usage
     * @param pPlayer Player to validate
     * @return true if player is in valid state
     */
    bool ValidatePlayerStateForSkill(CPlayer *pPlayer) const;

    /**
     * @brief Calculates skill resource cost
     * @param pPlayer Player casting skill
     * @param skillData Skill data
     * @return Resource cost (SP, MP, etc.)
     */
    int CalculateSkillResourceCost(CPlayer *pPlayer, const SkillProcessData &skillData) const;

    /**
     * @brief Processes skill animation and effects
     * @param pPlayer Player casting skill
     * @param skillData Skill data
     */
    void ProcessSkillAnimation(CPlayer *pPlayer, const SkillProcessData &skillData);

    /**
     * @brief Handles skill failure cleanup
     * @param pPlayer Player whose skill failed
     * @param skillData Skill data
     * @param result Failure result code
     */
    void HandleSkillFailure(CPlayer *pPlayer, const SkillProcessData &skillData, SkillProcessResult result);
};
