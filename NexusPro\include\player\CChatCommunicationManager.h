#pragma once

/**
 * @file CChatCommunicationManager.h
 * @brief Chat and Communication Management System for NexusPro
 * 
 * Manages comprehensive chat and communication operations including all chat types,
 * whisper systems, guild communications, party chat, race communications, and
 * specialized messaging systems with anti-spam protection and chat locks.
 * 
 * Original decompiled functions:
 * - pc_ChatAllRequest: 0x140092860
 * - pc_ChatWhisperRequest: Various addresses
 * - pc_ChatGuildRequest: 0x140091B30
 * - pc_ChatPartyRequest: 0x140091880
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @category Player Extended Systems - Phase 2 Batch 8
 * @note STRICT ADHERENCE TO DECOMPILED SOURCE: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */

#include "../common/WindowsTypes.h"
#include "../common/RFProtocol.h"
#include "../common/Stubs.h"
#include "../database/CUserDB.h"
#include "../storage/_STORAGE_LIST.h"
#include "../chat/CChatStealSystem.h"
#include "../network/_announ_message_receipt_udp.h"
#include <memory>
#include <mutex>
#include <string>
#include <vector>

// Forward declarations
class CPlayer;
class CUserDB;
class CChatStealSystem;
class CGameObject;
struct _announ_message_receipt_udp;
struct _STORAGE_LIST;

/**
 * @enum ChatType
 * @brief Types of chat communications
 */
enum ChatType {
    CHAT_ALL = 0,                                    ///< All chat (global)
    CHAT_CIRCLE = 1,                                 ///< Circle/local area chat
    CHAT_FAR = 2,                                    ///< Far distance chat
    CHAT_PARTY = 3,                                  ///< Party chat
    CHAT_GUILD = 4,                                  ///< Guild chat
    CHAT_RACE = 5,                                   ///< Race chat
    CHAT_WHISPER = 6,                                ///< Whisper/private message
    CHAT_MAP = 7,                                    ///< Map-wide chat
    CHAT_OPERATOR = 8,                               ///< Operator chat
    CHAT_GM_NOTICE = 9,                              ///< GM notice
    CHAT_RACE_BOSS = 10,                             ///< Race boss chat
    CHAT_RACE_BOSS_CRY = 11,                         ///< Race boss cry message
    CHAT_MULTI_FAR = 12,                             ///< Multi-target far chat
    CHAT_TRADE_REQUEST = 13,                         ///< Trade request message
    CHAT_REPRESENTATION = 14,                        ///< Representation chat
    CHAT_GUILD_EST_SEN = 15                          ///< Guild establishment senate
};

/**
 * @enum ChatRestriction
 * @brief Chat restriction types
 */
enum ChatRestriction {
    RESTRICTION_NONE = 0x00,                         ///< No restrictions
    RESTRICTION_CHAT_LOCK = 0x01,                    ///< Chat locked by user
    RESTRICTION_PUNISHMENT = 0x02,                   ///< Punishment restriction
    RESTRICTION_MAP_LOADING = 0x04,                  ///< Map loading restriction
    RESTRICTION_INVALID_LOCATION = 0x08,             ///< Invalid location restriction
    RESTRICTION_WHISPER_BLOCK = 0x10,                ///< Whisper blocked
    RESTRICTION_PARTY_BLOCK = 0x20,                  ///< Party requests blocked
    RESTRICTION_GUILD_BATTLE = 0x40,                 ///< Guild battle restriction
    RESTRICTION_TRADE_BLOCK = 0x80                   ///< Trade blocked
};

/**
 * @struct ChatMessage
 * @brief Chat message data structure
 */
struct ChatMessage {
    ChatType type;                                   ///< Type of chat message
    std::string content;                             ///< Message content
    std::string senderName;                          ///< Sender's name
    std::string targetName;                          ///< Target name (for whispers)
    DWORD dwSenderSerial;                            ///< Sender's serial number
    DWORD dwTargetSerial;                            ///< Target's serial number (for whispers)
    DWORD dwTimestamp;                               ///< Message timestamp
    bool bRequiresItem;                              ///< Whether message requires special item
    
    /**
     * @brief Default constructor
     */
    ChatMessage();
    
    /**
     * @brief Constructor with parameters
     */
    ChatMessage(ChatType chatType, const std::string& message, const std::string& sender);
};

/**
 * @class CChatCommunicationManager
 * @brief Manages chat and communication operations for players
 * 
 * This class handles all aspects of chat and communication functionality including:
 * - All chat types with appropriate validation and restrictions
 * - Whisper system with blocking and anti-spam protection
 * - Guild and party communication systems
 * - Race-based communication and boss messaging
 * - Operator and GM communication channels
 * - Chat item requirements and durability management
 * - Anti-spam protection and chat steal system integration
 * 
 * Based on decompiled source at: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */
class CChatCommunicationManager {
public:
    /**
     * @brief Default constructor
     * Initializes chat communication manager with RAII resource management
     */
    CChatCommunicationManager();

    /**
     * @brief Virtual destructor for proper cleanup
     * Ensures all communication operations are properly completed and resources released
     */
    virtual ~CChatCommunicationManager();

    // === Core Chat Operations ===
    
    /**
     * @brief Processes all chat request
     * 
     * Handles global chat messages including:
     * - User database and chat lock validation
     * - Punishment status checking
     * - Current location validation
     * - Map loading status verification
     * - Chat item requirement checking and durability management
     * - Chat steal system integration
     * - Debug memory initialization (0xCCCCCCCC pattern)
     * 
     * @param pPlayer Player sending the message
     * @param pwszChatData Chat message content
     * 
     * Original address: 0x140092860
     * Function: ?pc_ChatAllRequest@CPlayer@@QEAAXPEAD@Z
     */
    void pc_ChatAllRequest(CPlayer *pPlayer, char *pwszChatData);

    /**
     * @brief Processes circle/local area chat request
     * 
     * Handles local area chat messages with proximity validation.
     * 
     * @param pPlayer Player sending the message
     * @param pwszChatData Chat message content
     * 
     * Original address: 0x140090C20
     * Function: ?pc_ChatCircleRequest@CPlayer@@QEAAXPEAD@Z
     */
    void pc_ChatCircleRequest(CPlayer *pPlayer, char *pwszChatData);

    /**
     * @brief Processes far distance chat request
     * 
     * Handles long-distance chat messages with special requirements.
     * 
     * @param pPlayer Player sending the message
     * @param pwszChatData Chat message content
     * @param dwTargetSerial Target player serial (optional)
     * 
     * Original address: 0x140091500
     * Function: ?pc_ChatFarRequest@CPlayer@@QEAAXPEAD0@Z
     */
    void pc_ChatFarRequest(CPlayer *pPlayer, char *pwszChatData, DWORD dwTargetSerial = 0);

    /**
     * @brief Processes party chat request
     * 
     * Handles party communication with party membership validation.
     * 
     * @param pPlayer Player sending the message
     * @param pwszChatData Chat message content
     * 
     * Original address: 0x140091880
     * Function: ?pc_ChatPartyRequest@CPlayer@@QEAAXPEAD@Z
     */
    void pc_ChatPartyRequest(CPlayer *pPlayer, char *pwszChatData);

    /**
     * @brief Processes guild chat request
     * 
     * Handles guild communication with guild membership validation.
     * 
     * @param pPlayer Player sending the message
     * @param dwGuildSerial Guild serial number
     * @param pwszChatData Chat message content
     * 
     * Original address: 0x140091B30
     * Function: ?pc_ChatGuildRequest@CPlayer@@QEAAXKPEAD@Z
     */
    void pc_ChatGuildRequest(CPlayer *pPlayer, DWORD dwGuildSerial, char *pwszChatData);

    /**
     * @brief Processes race chat request
     * 
     * Handles race-based communication with race validation.
     * 
     * @param pPlayer Player sending the message
     * @param pwszChatData Chat message content
     * 
     * Original address: 0x140091E70
     * Function: ?pc_ChatRaceRequest@CPlayer@@QEAAXPEAD@Z
     */
    void pc_ChatRaceRequest(CPlayer *pPlayer, char *pwszChatData);

    /**
     * @brief Processes whisper request
     * 
     * Handles private messaging with target validation and blocking checks.
     * 
     * @param pPlayer Player sending the whisper
     * @param pwszTargetName Target player name
     * @param pwszChatData Whisper message content
     */
    void pc_ChatWhisperRequest(CPlayer *pPlayer, char *pwszTargetName, char *pwszChatData);

    // === Specialized Chat Operations ===
    
    /**
     * @brief Processes map-wide chat request
     * 
     * Handles map-wide announcements with appropriate permissions.
     * 
     * @param pPlayer Player sending the message
     * @param pwszChatData Chat message content
     * 
     * Original address: 0x140092480
     * Function: ?pc_ChatMapRequest@CPlayer@@QEAAXPEAD@Z
     */
    void pc_ChatMapRequest(CPlayer *pPlayer, char *pwszChatData);

    /**
     * @brief Processes operator chat request
     * 
     * Handles operator-level communication with privilege validation.
     * 
     * @param pPlayer Player sending the message
     * @param byOperatorLevel Operator level requirement
     * @param pwszChatData Chat message content
     * 
     * Original address: 0x140090B00
     * Function: ?pc_ChatOperatorRequest@CPlayer@@QEAAXEPEAD@Z
     */
    void pc_ChatOperatorRequest(CPlayer *pPlayer, BYTE byOperatorLevel, char *pwszChatData);

    /**
     * @brief Processes GM notice request
     * 
     * Handles GM notice broadcasts with GM privilege validation.
     * 
     * @param pPlayer GM sending the notice
     * @param pwszNoticeData Notice content
     * 
     * Original address: 0x140092410
     * Function: ?pc_ChatGmNoticeRequest@CPlayer@@QEAAXPEAD@Z
     */
    void pc_ChatGmNoticeRequest(CPlayer *pPlayer, char *pwszNoticeData);

    /**
     * @brief Processes race boss chat request
     * 
     * Handles race boss communication with leadership validation.
     * 
     * @param pPlayer Race boss sending the message
     * @param pwszChatData Chat message content
     * 
     * Original address: 0x140092CD0
     * Function: ?pc_ChatRaceBossRequest@CPlayer@@QEAAXPEAD@Z
     */
    void pc_ChatRaceBossRequest(CPlayer *pPlayer, char *pwszChatData);

    /**
     * @brief Processes multi-target far chat request
     * 
     * Handles far chat to multiple specific targets.
     * 
     * @param pPlayer Player sending the message
     * @param byTargetCount Number of targets
     * @param pTargetNames Array of target names
     * @param pwszChatData Chat message content
     * 
     * Original address: 0x1400939E0
     * Function: ?pc_ChatMultiFarRequest@CPlayer@@QEAAXEPEAU_w_namePEAD@Z
     */
    void pc_ChatMultiFarRequest(CPlayer *pPlayer, BYTE byTargetCount, struct _w_name *pTargetNames, char *pwszChatData);

    // === Chat Validation and Restrictions ===
    
    /**
     * @brief Validates chat permissions for player
     * @param pPlayer Player to validate
     * @param chatType Type of chat to validate
     * @return Restriction flags (0 if no restrictions)
     */
    DWORD ValidateChatPermissions(CPlayer *pPlayer, ChatType chatType) const;

    /**
     * @brief Checks if player is chat locked
     * @param pPlayer Player to check
     * @return true if player has chat locked
     */
    bool IsChatLocked(CPlayer *pPlayer) const;

    /**
     * @brief Checks if player is punished for chat
     * @param pPlayer Player to check
     * @return true if player is punished
     */
    bool IsChatPunished(CPlayer *pPlayer) const;

    /**
     * @brief Validates chat item requirements
     * @param pPlayer Player to validate
     * @param chatType Type of chat requiring item
     * @return true if player has required item
     */
    bool ValidateChatItemRequirements(CPlayer *pPlayer, ChatType chatType) const;

    /**
     * @brief Processes chat item durability
     * @param pPlayer Player using chat item
     * @param pItem Chat item being used
     * @return true if item durability was successfully processed
     */
    bool ProcessChatItemDurability(CPlayer *pPlayer, _STORAGE_LIST::_db_con *pItem);

    // === Blocking and Anti-Spam ===
    
    /**
     * @brief Sets whisper block status
     * @param pPlayer Player to set block for
     * @param bBlocked Whether to block whispers
     */
    void SetWhisperBlock(CPlayer *pPlayer, bool bBlocked);

    /**
     * @brief Sets party request block status
     * @param pPlayer Player to set block for
     * @param bBlocked Whether to block party requests
     */
    void SetPartyRequestBlock(CPlayer *pPlayer, bool bBlocked);

    /**
     * @brief Sets trade block status
     * @param pPlayer Player to set block for
     * @param bBlocked Whether to block trade requests
     */
    void SetTradeBlock(CPlayer *pPlayer, bool bBlocked);

    /**
     * @brief Sets guild battle block status
     * @param pPlayer Player to set block for
     * @param bBlocked Whether to block guild battle participation
     */
    void SetGuildBattleBlock(CPlayer *pPlayer, bool bBlocked);

    // === Chat System Integration ===
    
    /**
     * @brief Sets chat steal system reference
     * @param pChatStealSystem Chat steal system instance
     */
    void SetChatStealSystem(CChatStealSystem *pChatStealSystem);

    /**
     * @brief Gets chat steal system reference
     * @return Pointer to chat steal system
     */
    CChatStealSystem* GetChatStealSystem() const { return m_pChatStealSystem; }

protected:
    // === Core System Data ===
    CChatStealSystem* m_pChatStealSystem;            ///< Chat steal system reference
    
    // === Configuration ===
    bool m_bInitialized;                             ///< Initialization status
    DWORD m_dwLastChatTime;                          ///< Last chat timestamp for anti-spam
    
    // === Configuration Constants ===
    static constexpr DWORD CHAT_COOLDOWN_MS = 1000;  ///< Chat cooldown (1 second)
    static constexpr DWORD MAX_CHAT_LENGTH = 512;    ///< Maximum chat message length
    static constexpr DWORD WHISPER_COOLDOWN_MS = 500; ///< Whisper cooldown (0.5 seconds)
    static constexpr DWORD FAR_CHAT_ITEM_CODE = 30101; ///< Item code for far chat
    static constexpr DWORD ALL_CHAT_ITEM_CODE = 30102; ///< Item code for all chat
    
    // === Security and Validation ===
    mutable std::mutex m_chatMutex;                  ///< Mutex for thread-safe operations

private:
    // === Helper Methods ===
    
    /**
     * @brief Performs debug memory initialization
     * 
     * Initializes memory with debug pattern (0xCCCCCCCC) as per
     * decompiled source implementation.
     */
    void InitializeDebugMemory();

    /**
     * @brief Validates chat message content
     * @param pwszChatData Message content to validate
     * @return true if message content is valid
     */
    bool ValidateChatContent(const char *pwszChatData) const;

    /**
     * @brief Processes chat message broadcast
     * @param pSender Player sending the message
     * @param chatMessage Message to broadcast
     * @param chatType Type of chat for targeting
     */
    void BroadcastChatMessage(CPlayer *pSender, const ChatMessage &chatMessage, ChatType chatType);

    /**
     * @brief Logs chat operation for audit
     * @param pPlayer Player performing operation
     * @param strOperation Operation description
     * @param chatType Optional chat type
     */
    void LogChatOperation(CPlayer* pPlayer, const char* strOperation, ChatType chatType = CHAT_ALL) const;

    /**
     * @brief Validates player state for chat
     * @param pPlayer Player to validate
     * @return true if player is in valid state for chat
     */
    bool ValidatePlayerStateForChat(CPlayer *pPlayer) const;

    /**
     * @brief Applies chat cooldown
     * @param pPlayer Player to apply cooldown for
     * @param chatType Type of chat for cooldown calculation
     */
    void ApplyChatCooldown(CPlayer *pPlayer, ChatType chatType);

    /**
     * @brief Checks chat cooldown status
     * @param pPlayer Player to check cooldown for
     * @param chatType Type of chat to check
     * @return true if player can send chat (cooldown expired)
     */
    bool CheckChatCooldown(CPlayer *pPlayer, ChatType chatType) const;
};
