#pragma once

/*
 * CAntiCheatManager.h - SECURITY ENHANCED
 * NexusPro RF Online Zone Server
 * 
 * Anti-cheat detection and prevention management system
 * Enhanced with Emergency Fix Methodology for RF Online compatibility
 * 
 * Original functionality based on decompiled RF Online server code
 * Enhanced with modern C++ standards and comprehensive security measures
 */

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"
#include "../authentication/CHackShieldExSystem.h"
#include "INationGameGuardSystem.h"

// Forward declarations
class CMainThread;
class CNetworkManager;
class CSecurityManager;
class CSystemManager;

// Anti-cheat detection types
enum AntiCheatDetectionType {
    DETECTION_NONE = 0,
    DETECTION_SPEED_HACK = 1,
    DETECTION_MEMORY_HACK = 2,
    DETECTION_PACKET_HACK = 3,
    DETECTION_DUPLICATE_LOGIN = 4,
    DETECTION_INVALID_CLIENT = 5,
    DETECTION_PROCESS_INJECTION = 6,
    DETECTION_FILE_INTEGRITY = 7,
    DETECTION_SYSTEM_INTEGRITY = 8
};

// Anti-cheat action types
enum AntiCheatActionType {
    ACTION_NONE = 0,
    ACTION_LOG_ONLY = 1,
    ACTION_WARNING = 2,
    ACTION_KICK = 3,
    ACTION_TEMPORARY_BAN = 4,
    ACTION_PERMANENT_BAN = 5
};

// Anti-cheat detection result
struct AntiCheatDetectionResult {
    AntiCheatDetectionType detectionType;
    DWORD dwPlayerSerial;
    DWORD dwDetectionTime;
    char szPlayerName[32];
    char szDetectionDetails[256];
    DWORD dwSeverityLevel;
    bool bActionTaken;
    AntiCheatActionType actionType;
};

// Anti-cheat configuration
struct AntiCheatConfig {
    bool bEnabled;
    bool bHackShieldEnabled;
    bool bGameGuardEnabled;
    bool bSpeedHackDetection;
    bool bMemoryHackDetection;
    bool bPacketHackDetection;
    bool bDuplicateLoginDetection;
    bool bFileIntegrityCheck;
    bool bSystemIntegrityCheck;
    DWORD dwScanInterval;
    DWORD dwMaxDetectionsPerMinute;
    DWORD dwBanDuration;
    DWORD dwWarningThreshold;
    DWORD dwKickThreshold;
    DWORD dwBanThreshold;
};

// Anti-cheat statistics
struct AntiCheatStatistics {
    DWORD dwTotalScans;
    DWORD dwTotalDetections;
    DWORD dwSpeedHackDetections;
    DWORD dwMemoryHackDetections;
    DWORD dwPacketHackDetections;
    DWORD dwDuplicateLoginDetections;
    DWORD dwFileIntegrityFailures;
    DWORD dwSystemIntegrityFailures;
    DWORD dwPlayersWarned;
    DWORD dwPlayersKicked;
    DWORD dwPlayersBanned;
    DWORD dwLastScanTime;
    DWORD dwAverageScanTime;
    DWORD dwMaxScanTime;
};

/*
 * CAntiCheatManager - Anti-cheat detection and prevention management
 * Manages all anti-cheat systems and detection mechanisms
 * Enhanced with comprehensive security measures and RF Online compatibility
 */
class CAntiCheatManager {
public:
    // Singleton access
    static CAntiCheatManager& Instance();

    // Core lifecycle methods
    bool Initialize();
    void Shutdown();
    void Update();

    // Anti-cheat system management
    bool InitializeAntiCheatSystems();
    void ShutdownAntiCheatSystems();
    bool IsAntiCheatActive() const;

    // Detection methods
    bool ScanPlayer(DWORD dwPlayerSerial);
    bool DetectSpeedHack(DWORD dwPlayerSerial);
    bool DetectMemoryHack(DWORD dwPlayerSerial);
    bool DetectPacketHack(DWORD dwPlayerSerial);
    bool DetectDuplicateLogin(DWORD dwPlayerSerial);
    bool ValidateFileIntegrity(DWORD dwPlayerSerial);
    bool ValidateSystemIntegrity(DWORD dwPlayerSerial);

    // Action methods
    bool TakeAntiCheatAction(const AntiCheatDetectionResult& result);
    bool WarnPlayer(DWORD dwPlayerSerial, const char* szReason);
    bool KickPlayer(DWORD dwPlayerSerial, const char* szReason);
    bool BanPlayer(DWORD dwPlayerSerial, const char* szReason, DWORD dwDuration);

    // Configuration and statistics
    const AntiCheatConfig& GetConfig() const { return m_Config; }
    const AntiCheatStatistics& GetStatistics() const { return m_Statistics; }
    void ResetStatistics();

    // System integration
    bool RegisterDetectionCallback(void (*callback)(const AntiCheatDetectionResult&));
    void LogDetection(const AntiCheatDetectionResult& result);

private:
    // Private constructor for singleton
    CAntiCheatManager();
    ~CAntiCheatManager();

    // Prevent copying
    CAntiCheatManager(const CAntiCheatManager&) = delete;
    CAntiCheatManager& operator=(const CAntiCheatManager&) = delete;

    // Internal methods
    bool LoadConfiguration();
    bool InitializeHackShield();
    bool InitializeGameGuard();
    void ShutdownHackShield();
    void ShutdownGameGuard();
    bool ProcessDetectionResult(const AntiCheatDetectionResult& result);
    void UpdateStatistics();

    // Member variables
    static CAntiCheatManager* s_pInstance;
    static std::mutex s_InstanceMutex;

    bool m_bInitialized;
    bool m_bActive;
    AntiCheatConfig m_Config;
    AntiCheatStatistics m_Statistics;

    // Anti-cheat systems
    CHackShieldExSystem* m_pHackShield;
    INationGameGuardSystem* m_pGameGuard;

    // Detection tracking
    std::vector<AntiCheatDetectionResult> m_DetectionHistory;
    std::map<DWORD, DWORD> m_PlayerDetectionCounts;
    std::map<DWORD, DWORD> m_PlayerLastScanTime;

    // Synchronization
    mutable std::mutex m_ConfigMutex;
    mutable std::mutex m_StatisticsMutex;
    mutable std::mutex m_DetectionMutex;
    mutable std::mutex m_SystemMutex;

    // Callback system
    std::vector<void (*)(const AntiCheatDetectionResult&)> m_DetectionCallbacks;

    // Performance tracking
    DWORD m_dwLastUpdateTime;
    DWORD m_dwUpdateInterval;
    DWORD m_dwScanStartTime;
};
