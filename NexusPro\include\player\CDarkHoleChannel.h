﻿#pragma once

/**
 * @file CDarkHoleChannel.h
 * @brief Dark Hole Channel Management System for NexusPro
 *
 * Manages dark hole dungeon channels, including player entry validation,
 * party management, quest progression, and channel lifecycle. Dark holes
 * are instanced dungeons with complex entry requirements and party mechanics.
 *
 * Original decompiled functions:
 * - CanYouEnterHole: 0x14026A710
 * - ClearMember: 0x14026AB00
 * - OpenDungeon: Various addresses
 *
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @category Player Extended Systems - Phase 2 Batch 7
 * @note STRICT ADHERENCE TO DECOMPILED SOURCE: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */

#include "../common/WindowsTypes.h"
#include "../common/RFProtocol.h"
#include "../common/Stubs.h"
#include "../party/CPartyPlayer.h"
#include "../effect/_effect_parameter.h"
#include <memory>
#include <array>
#include <mutex>

// Forward declarations
class CA8h;
class CB8h;
class CC0h;
class CDarkHole;
class CExtDummy;
class CGameObject;
class CIndexList;
class CMapData;
class CMonster;
class CNetProcess;
class CPartyPlayer;
class CPlayer;
class CPlayerDB;
class CRecordData;
struct _dh_mission_setup;
struct _dh_player_mgr;
struct _dh_quest_setup;

/**
 * @class CDarkHoleChannel
 * @brief Manages dark hole dungeon channel operations
 *
 * This class handles all aspects of dark hole channel functionality including:
 * - Player entry validation and permission checking (max 32 players)
 * - Party-based and individual access control
 * - Quest progression and mission management
 * - Channel member management with degree-based restrictions
 * - Dungeon opening and lifecycle management
 * - Real-time messaging and communication
 *
 * Based on decompiled source at: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */
class CDarkHoleChannel {
public:
    /**
     * @brief Default constructor
     * Initializes dark hole channel with RAII resource management
     */
    CDarkHoleChannel();

    /**
     * @brief Virtual destructor for proper cleanup
     * Ensures all channel operations are properly terminated and resources released
     */
    virtual ~CDarkHoleChannel();

    // === Core Entry and Validation ===

    /**
     * @brief Validates if player can enter the dark hole
     *
     * Performs comprehensive entry validation including:
     * - Channel capacity check (max 32 players)
     * - Duplicate player check
     * - Party membership validation for party-only holes
     * - Degree-based access control
     * - Effect parameter validation
     *
     * @param pEnter Player attempting to enter
     * @param a3 Effect parameter for validation
     * @return true if player can enter the hole
     *
     * Original address: 0x14026A710
     * Function: ?CanYouEnterHole@CDarkHoleChannel@@QEAA_NPEAVCPlayer@@@Z
     */
    bool CanYouEnterHole(CPlayer *pEnter, float a3);

    /**
     * @brief Clears member from channel
     *
     * Removes player from the dark hole channel and handles cleanup.
     * Updates member list and manages disconnection scenarios.
     *
     * @param pMember Player to remove from channel
     * @param bDisconnect Whether this is due to disconnection
     * @param poutPlayerPos Output position data
     * @return Status code of member clearing
     *
     * Original address: 0x14026AB00
     * Function: ?ClearMember@CDarkHoleChannel@@QEAA_NPEAVCPlayer@@_NPEAU_pos@_dh_player_mgr@@@Z
     */
    char ClearMember(CPlayer *pMember, bool bDisconnect, _dh_player_mgr::_pos *poutPlayerPos);

    /**
     * @brief Opens dungeon for quest progression
     *
     * Initializes and opens the dungeon instance for the specified quest
     * with proper layer management and player coordination.
     *
     * @param pQuestSetup Quest configuration data
     * @param nLayerIndex Map layer index
     * @param pOpener Player opening the dungeon
     * @param pHoleObj Dark hole object reference
     */
    void OpenDungeon(_dh_quest_setup *pQuestSetup, int nLayerIndex, CPlayer *pOpener, CDarkHole *pHoleObj);

    // === Member Management ===

    /**
     * @brief Gets player information by serial
     * @param dwSerial Player serial number
     */
    void GetPlayerInfo(unsigned int dwSerial);

    /**
     * @brief Checks if player is party member of opener
     * @param pOpener Player who opened the hole
     * @return true if player is party member
     */
    bool IsOpenPartyMember(CPlayer *pOpener);

    /**
     * @brief Advances to next mission for other questers
     * @param pLeader Party leader
     * @param pNextMission Next mission configuration
     */
    void NextMissionOtherQuester(CPlayer *pLeader, _dh_mission_setup *pNextMission);

    // === Messaging System ===

    /**
     * @brief Sends channel close notification
     */
    void SendMsg_ChannelClose();

    /**
     * @brief Sends gate destruction message
     * @param byType Gate type
     * @param pSend Send data
     * @param nSize Data size
     */
    void SendMsg_GateDestroy(char *byType, char *pSend, int nSize);

    /**
     * @brief Sends job count update
     * @param nJobIndex Job index
     * @param nCount Current count
     */
    void SendMsg_JobCount(int nJobIndex, int nCount);

    /**
     * @brief Sends job completion notification
     * @param nJobIndex Completed job index
     */
    void SendMsg_JobPass(int nJobIndex);

    /**
     * @brief Sends mission completion notification
     */
    void SendMsg_MissionPass();

    /**
     * @brief Sends new mission notification
     */
    void SendMsg_NewMission();

    /**
     * @brief Sends portal opening by reaction
     * @param nPortalIndex Portal index to open
     */
    void SendMsg_OpenPortalByReact(int nPortalIndex);

    /**
     * @brief Sends portal opening by result
     * @param nPortalIndex Portal index to open
     */
    void SendMsg_OpenPortalByResult(int nPortalIndex);

    /**
     * @brief Sends quest completion notification
     */
    void SendMsg_QuestPass();

    /**
     * @brief Sends real-time limit time addition
     * @param nAddSec Additional seconds
     * @param pMsg Message to display
     */
    void SendMsg_RealAddLimTime(int nAddSec, char *pMsg);

    /**
     * @brief Sends real-time message information
     * @param pMsg Message to broadcast
     */
    void SendMsg_RealMsgInform(char *pMsg);

    /**
     * @brief Sends timeout notification
     */
    void SendMsg_TimeOut();

    // === Utility Methods ===

    /**
     * @brief Shares item to monster
     * @param a2 Item data parameter
     */
    void ShareItemToMonster(signed __int64 a2);

    /**
     * @brief Checks and sends new mission message
     */
    void CheckSendNewMissionMsg();

    // === Channel Information ===

    /**
     * @brief Gets current member count
     * @return Number of active members in channel
     */
    int GetCurrentMemberNum() const;

    /**
     * @brief Gets maximum channel capacity
     * @return Maximum number of players (32)
     */
    static constexpr int GetMaxCapacity() { return MAX_CHANNEL_MEMBERS; }

protected:
    // === Core Channel Data (based on decompiled source) ===
    std::array<_dh_player_mgr, MAX_CHANNEL_MEMBERS> m_Quester; ///< Channel member array (32 players max)
    _dh_quest_setup* m_pQuestSetup;                  ///< Quest configuration
    CPartyPlayer* m_pPartyMng;                       ///< Party management reference
    DWORD m_dwOpenerSerial;                          ///< Serial of player who opened the hole
    int m_nOpenerDegree;                             ///< Degree/rank of opener for access control

    // === Channel State ===
    bool m_bActive;                                  ///< Channel active status
    DWORD m_dwChannelID;                             ///< Unique channel identifier

    // === Configuration ===
    static constexpr int MAX_CHANNEL_MEMBERS = 32;   ///< Maximum players per channel
    static constexpr int MAX_PARTY_MEMBERS = 8;      ///< Maximum party members to check
    static constexpr int EFFECT_HAVE_PARAM = 50;     ///< Effect parameter for validation

    // === Security and Validation ===
    mutable std::mutex m_channelMutex;               ///< Mutex for thread-safe operations

private:
    // === Helper Methods ===

    /**
     * @brief Validates player degree access
     * @param pPlayer Player to validate
     * @return true if player has appropriate degree access
     */
    bool ValidatePlayerDegree(CPlayer* pPlayer) const;

    /**
     * @brief Checks if player is already in channel
     * @param dwSerial Player serial to check
     * @return true if player is already a member
     */
    bool IsPlayerInChannel(DWORD dwSerial) const;

    /**
     * @brief Validates party membership for party-only holes
     * @param pPlayer Player to validate
     * @return true if player is valid party member
     */
    bool ValidatePartyMembership(CPlayer* pPlayer) const;

    /**
     * @brief Performs debug memory initialization
     *
     * Initializes memory with debug pattern (0xCCCCCCCC) as per
     * decompiled source implementation.
     */
    void InitializeDebugMemory();

    /**
     * @brief Logs channel operation for audit
     * @param pPlayer Player performing operation
     * @param strOperation Operation description
     */
    void LogChannelOperation(CPlayer* pPlayer, const char* strOperation) const;
};
