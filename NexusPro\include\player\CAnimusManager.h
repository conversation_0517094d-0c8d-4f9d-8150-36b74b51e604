#pragma once

/**
 * @file CAnimusManager.h
 * @brief Animus (Pet/Companion) Management System for NexusPro
 * 
 * Manages animus (pet/companion) operations including experience calculation,
 * level progression, PC bang bonuses, master-animus relationships, and
 * animus-specific combat and skill systems.
 * 
 * Original decompiled functions:
 * - AlterExp: 0x1401265A0
 * - AlterExp_MasterReport: 0x1401292B0
 * - ChangeTarget_MasterCommand: 0x140128E40
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @category Player Extended Systems - Phase 2 Batch 7
 * @note STRICT ADHERENCE TO DECOMPILED SOURCE: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */

#include "../common/WindowsTypes.h"
#include "../common/RFProtocol.h"
#include "../common/Stubs.h"
#include "../time/TimeLimitMgr.h"
#include <memory>
#include <mutex>
#include <cmath>

// Forward declarations
class CAnimus;
class CPlayer;
class CCharacter;
struct _animus_fld;

/**
 * @enum AnimusExpType
 * @brief Types of experience alterations for animus
 */
enum AnimusExpType {
    EXP_COMBAT = 0,                                  ///< Combat experience
    EXP_SKILL = 1,                                   ///< Skill usage experience
    EXP_QUEST = 2,                                   ///< Quest completion experience
    EXP_BONUS = 3                                    ///< Bonus experience (PC bang, etc.)
};

/**
 * @enum AnimusCommandType
 * @brief Types of master commands for animus
 */
enum AnimusCommandType {
    CMD_ATTACK = 0,                                  ///< Attack command
    CMD_FOLLOW = 1,                                  ///< Follow command
    CMD_STAY = 2,                                    ///< Stay/guard command
    CMD_RETURN = 3                                   ///< Return to master command
};

/**
 * @struct AnimusExpData
 * @brief Data structure for animus experience calculations
 */
struct AnimusExpData {
    __int64 nBaseExp;                                ///< Base experience amount
    __int64 nBonusExp;                               ///< Bonus experience from PC bang
    double dMultiplier;                              ///< Experience multiplier
    bool bPcBangBonus;                               ///< PC bang bonus applied
    bool bMasterReport;                              ///< Master reporting enabled
    
    /**
     * @brief Default constructor
     */
    AnimusExpData();
};

/**
 * @class CAnimusManager
 * @brief Manages animus (pet/companion) operations and progression
 * 
 * This class handles all aspects of animus functionality including:
 * - Experience calculation with PC bang bonuses
 * - Level progression and maximum level validation
 * - Master-animus relationship management
 * - Command processing and target management
 * - Combat integration and skill progression
 * - Time limit penalty application
 * 
 * Based on decompiled source at: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */
class CAnimusManager {
public:
    /**
     * @brief Default constructor
     * Initializes animus manager with RAII resource management
     */
    CAnimusManager();

    /**
     * @brief Virtual destructor for proper cleanup
     * Ensures all animus operations are properly completed and resources released
     */
    virtual ~CAnimusManager();

    // === Core Experience Management ===
    
    /**
     * @brief Alters animus experience with bonuses and validation
     * 
     * Performs comprehensive experience alteration including:
     * - Master validation and operator status checking
     * - Maximum level validation
     * - PC bang bonus calculation (20% bonus)
     * - Time limit penalty application
     * - Experience rate multiplier application
     * - Master reporting for experience changes
     * - Debug memory initialization (0xCCCCCCCC pattern)
     * 
     * @param pAnimus Animus to alter experience for
     * @param nAddExp Experience amount to add
     * 
     * Original address: 0x1401265A0
     * Function: ?AlterExp@CAnimus@@QEAAX_J@Z
     */
    void AlterExp(CAnimus *pAnimus, __int64 nAddExp);

    /**
     * @brief Reports experience change to master
     * 
     * Notifies the master player about animus experience changes
     * and handles master-specific experience reporting.
     * 
     * @param pAnimus Animus that gained experience
     * @param nExpChange Amount of experience changed
     * 
     * Original address: 0x1401292B0
     * Function: ?AlterExp_MasterReport@CAnimus@@QEAAX_J@Z
     */
    void AlterExp_MasterReport(CAnimus *pAnimus, __int64 nExpChange);

    // === Command and Target Management ===
    
    /**
     * @brief Changes animus target based on master command
     * 
     * Processes master commands to change animus target and behavior.
     * Handles target validation and command execution.
     * 
     * @param pAnimus Animus to command
     * @param pNewTarget New target character
     * @return true if target change was successful
     * 
     * Original address: 0x140128E40
     * Function: ?ChangeTarget_MasterCommand@CAnimus@@QEAA_NPEAVCCharacter@@@Z
     */
    bool ChangeTarget_MasterCommand(CAnimus *pAnimus, CCharacter *pNewTarget);

    /**
     * @brief Processes master command for animus
     * @param pAnimus Animus to command
     * @param cmdType Type of command to execute
     * @param pTarget Optional target for command
     * @return true if command was processed successfully
     */
    bool ProcessMasterCommand(CAnimus *pAnimus, AnimusCommandType cmdType, CCharacter *pTarget = nullptr);

    // === Level and Progression Management ===
    
    /**
     * @brief Gets maximum level for animus
     * @param pAnimus Animus to check
     * @return Maximum level allowed
     */
    BYTE GetMaxLevel(CAnimus *pAnimus) const;

    /**
     * @brief Validates if animus can gain experience
     * @param pAnimus Animus to validate
     * @return true if animus can gain experience
     */
    bool CanGainExperience(CAnimus *pAnimus) const;

    /**
     * @brief Calculates PC bang bonus for animus
     * @param pAnimus Animus to calculate bonus for
     * @param nBaseExp Base experience amount
     * @return Experience amount with PC bang bonus applied
     */
    __int64 CalculatePcBangBonus(CAnimus *pAnimus, __int64 nBaseExp) const;

    /**
     * @brief Applies time limit penalty to experience
     * @param pAnimus Animus to apply penalty for
     * @param nExp Experience amount to modify
     * @return Experience amount with penalty applied
     */
    __int64 ApplyTimeLimitPenalty(CAnimus *pAnimus, __int64 nExp) const;

    // === Master-Animus Relationship ===
    
    /**
     * @brief Validates master-animus relationship
     * @param pAnimus Animus to validate
     * @return true if animus has valid master
     */
    bool ValidateMasterRelationship(CAnimus *pAnimus) const;

    /**
     * @brief Gets animus master player
     * @param pAnimus Animus to get master for
     * @return Pointer to master player, nullptr if none
     */
    CPlayer* GetAnimusMaster(CAnimus *pAnimus) const;

    /**
     * @brief Sets animus master player
     * @param pAnimus Animus to set master for
     * @param pMaster New master player
     * @return true if master was set successfully
     */
    bool SetAnimusMaster(CAnimus *pAnimus, CPlayer *pMaster);

    // === Combat and Skills ===
    
    /**
     * @brief Processes animus combat experience
     * @param pAnimus Animus that participated in combat
     * @param pTarget Combat target
     * @param nDamageDealt Damage dealt by animus
     */
    void ProcessCombatExperience(CAnimus *pAnimus, CCharacter *pTarget, int nDamageDealt);

    /**
     * @brief Processes animus skill usage experience
     * @param pAnimus Animus that used skill
     * @param nSkillCode Skill code used
     * @param bSuccessful Whether skill usage was successful
     */
    void ProcessSkillExperience(CAnimus *pAnimus, int nSkillCode, bool bSuccessful);

    // === Configuration and Settings ===
    
    /**
     * @brief Gets experience multiplier
     * @return Current experience multiplier
     */
    double GetExperienceMultiplier() const { return m_dExpMultiplier; }

    /**
     * @brief Sets experience multiplier
     * @param dMultiplier New experience multiplier
     */
    void SetExperienceMultiplier(double dMultiplier) { m_dExpMultiplier = dMultiplier; }

    /**
     * @brief Gets PC bang bonus percentage
     * @return PC bang bonus percentage (default 20%)
     */
    double GetPcBangBonusPercentage() const { return PC_BANG_BONUS_PERCENT; }

protected:
    // === Core Configuration ===
    double m_dExpMultiplier;                         ///< Experience multiplier
    bool m_bInitialized;                             ///< Initialization status
    
    // === System References ===
    TimeLimitMgr* m_pTimeLimitMgr;                   ///< Time limit manager reference
    
    // === Configuration Constants ===
    static constexpr double PC_BANG_BONUS_PERCENT = 0.2; ///< PC bang bonus (20%)
    static constexpr double BASE_EXP_MULTIPLIER = 1.0; ///< Base experience multiplier
    static constexpr BYTE DEFAULT_MAX_LEVEL = 255;   ///< Default maximum level
    static constexpr __int64 MIN_EXP_GAIN = 1;       ///< Minimum experience gain
    static constexpr __int64 MAX_EXP_GAIN = 999999999; ///< Maximum experience gain
    
    // === Security and Validation ===
    mutable std::mutex m_animusMutex;                ///< Mutex for thread-safe operations

private:
    // === Helper Methods ===
    
    /**
     * @brief Performs debug memory initialization
     * 
     * Initializes memory with debug pattern (0xCCCCCCCC) as per
     * decompiled source implementation.
     */
    void InitializeDebugMemory();

    /**
     * @brief Validates experience parameters
     * @param pAnimus Animus to validate
     * @param nExp Experience amount to validate
     * @return true if parameters are valid
     */
    bool ValidateExperienceParameters(CAnimus *pAnimus, __int64 nExp) const;

    /**
     * @brief Calculates final experience with all bonuses
     * @param pAnimus Animus to calculate for
     * @param nBaseExp Base experience amount
     * @return Final experience amount with all bonuses applied
     */
    __int64 CalculateFinalExperience(CAnimus *pAnimus, __int64 nBaseExp) const;

    /**
     * @brief Logs animus operation for audit
     * @param pAnimus Animus performing operation
     * @param strOperation Operation description
     * @param nValue Optional numeric value
     */
    void LogAnimusOperation(CAnimus* pAnimus, const char* strOperation, __int64 nValue = 0) const;

    /**
     * @brief Validates animus state for operations
     * @param pAnimus Animus to validate
     * @return true if animus is in valid state
     */
    bool ValidateAnimusState(CAnimus *pAnimus) const;

    /**
     * @brief Updates animus field data
     * @param pAnimus Animus to update
     * @param pFieldData New field data
     */
    void UpdateAnimusFieldData(CAnimus *pAnimus, _animus_fld *pFieldData);
};
