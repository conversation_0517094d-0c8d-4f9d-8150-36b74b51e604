#pragma once

/**
 * @file CTransformationManager.h
 * @brief Player Transformation Management System for NexusPro
 * 
 * Manages all player transformation operations including siege mode transformations,
 * mecha transformations, race specializations, weapon specializations, and general
 * transformation mechanics. Handles validation, equipment requirements, and state management.
 * 
 * Original decompiled functions:
 * - pc_TransformSiegeModeRequest: 0x1400F0880
 * - pc_Transformation: Various addresses
 * - IsSiegeMode: Various addresses
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @category Player Extended Systems - Phase 2 Batch 8
 * @note STRICT ADHERENCE TO DECOMPILED SOURCE: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */

#include "../common/WindowsTypes.h"
#include "../common/RFProtocol.h"
#include "../common/Stubs.h"
#include "../storage/_STORAGE_LIST.h"
#include "../database/CRecordData.h"
#include "../timer/CMyTimer.h"
#include <memory>
#include <mutex>
#include <array>

// Forward declarations
class CPlayer;
class CPlayerDB;
class CRecordData;
struct _base_fld;
struct _STORAGE_LIST;

/**
 * @enum TransformationType
 * @brief Types of player transformations
 */
enum TransformationType {
    TRANSFORM_NONE = 0,                              ///< No transformation
    TRANSFORM_SIEGE_MODE = 1,                        ///< Siege mode transformation
    TRANSFORM_MECHA = 2,                             ///< Mecha transformation
    TRANSFORM_RACE_SPECIALIZATION = 3,               ///< Race specialization
    TRANSFORM_WEAPON_SPECIALIZATION = 4,             ///< Weapon specialization
    TRANSFORM_GENERAL = 5                            ///< General transformation
};

/**
 * @enum SiegeModeResult
 * @brief Result codes for siege mode transformation requests
 */
enum SiegeModeResult {
    SIEGE_SUCCESS = 0,                               ///< Transformation successful
    SIEGE_INVALID_ITEM = 1,                          ///< Invalid item for transformation
    SIEGE_WRONG_TABLE_CODE = 2,                      ///< Wrong item table code
    SIEGE_ALREADY_SIEGE = 3,                         ///< Already in siege mode
    SIEGE_NO_DURABILITY = 4,                         ///< Item has no durability
    SIEGE_NO_WEAPON = 5,                             ///< No weapon equipped
    SIEGE_WEAPON_MISMATCH = 6,                       ///< Weapon type mismatch
    SIEGE_CIVIL_RESTRICTION = 7,                     ///< Civil equipment restriction
    SIEGE_EFFECT_RESTRICTION = 8,                    ///< Effect restriction
    SIEGE_RIDING_UNIT = 9,                           ///< Cannot transform while riding
    SIEGE_ITEM_LOCKED = 10                           ///< Item is locked
};

/**
 * @struct TransformationData
 * @brief Data structure for transformation operations
 */
struct TransformationData {
    TransformationType type;                         ///< Type of transformation
    WORD wItemSerial;                                ///< Item serial for transformation
    WORD wTransformType;                             ///< Specific transformation type
    DWORD dwDuration;                                ///< Transformation duration
    bool bCombatMode;                                ///< Combat mode flag
    bool bActive;                                    ///< Transformation active status
    
    /**
     * @brief Default constructor
     */
    TransformationData();
    
    /**
     * @brief Constructor with parameters
     */
    TransformationData(TransformationType transformType, WORD itemSerial, DWORD duration);
};

/**
 * @class CTransformationManager
 * @brief Manages player transformation operations and state
 * 
 * This class handles all aspects of player transformation functionality including:
 * - Siege mode transformation with equipment validation
 * - Mecha transformation for RF Online specific mechanics
 * - Race and weapon specialization transformations
 * - Transformation state management and timers
 * - Equipment compatibility and requirement checking
 * - Civil equipment restrictions and validation
 * 
 * Based on decompiled source at: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */
class CTransformationManager {
public:
    /**
     * @brief Default constructor
     * Initializes transformation manager with RAII resource management
     */
    CTransformationManager();

    /**
     * @brief Virtual destructor for proper cleanup
     * Ensures all transformation operations are properly completed and resources released
     */
    virtual ~CTransformationManager();

    // === Core Transformation Operations ===
    
    /**
     * @brief Processes siege mode transformation request
     * 
     * Handles siege mode transformation including:
     * - Item validation (table code 27 required)
     * - Current siege mode status checking
     * - Equipment compatibility validation
     * - Weapon type and specialization matching
     * - Civil equipment restrictions
     * - Durability and lock status checking
     * - Debug memory initialization (0xCCCCCCCC pattern)
     * 
     * @param pPlayer Player requesting transformation
     * @param wItemSerial Serial of transformation item
     * 
     * Original address: 0x1400F0880
     * Function: ?pc_TransformSiegeModeRequest@CPlayer@@QEAAXG@Z
     */
    void pc_TransformSiegeModeRequest(CPlayer *pPlayer, unsigned __int16 wItemSerial);

    /**
     * @brief Processes general transformation request
     * 
     * Handles general transformation operations for various transformation types.
     * 
     * @param pPlayer Player requesting transformation
     * @param wTransformType Type of transformation to apply
     * @param dwDuration Duration of transformation in milliseconds
     */
    void pc_Transformation(CPlayer *pPlayer, unsigned short wTransformType, unsigned long dwDuration);

    /**
     * @brief Processes mecha transformation request
     * 
     * Handles mecha transformation specific to RF Online mechanics.
     * 
     * @param pPlayer Player requesting mecha transformation
     * @param wMechaType Type of mecha to transform into
     * @param bCombatMode Whether to enter combat mode immediately
     */
    void pc_MechaTransform(CPlayer *pPlayer, unsigned short wMechaType, bool bCombatMode);

    /**
     * @brief Processes race specialization transformation
     * 
     * Handles race-specific specialization transformations.
     * 
     * @param pPlayer Player requesting race specialization
     * @param wSpecializationType Type of race specialization
     * @param nSpecializationLevel Level of specialization
     */
    void pc_RaceSpecialization(CPlayer *pPlayer, unsigned short wSpecializationType, int nSpecializationLevel);

    /**
     * @brief Processes weapon specialization transformation
     * 
     * Handles weapon-specific specialization transformations.
     * 
     * @param pPlayer Player requesting weapon specialization
     * @param wWeaponCategory Category of weapon to specialize in
     * @param nSpecializationLevel Level of specialization
     */
    void pc_WeaponSpecialization(CPlayer *pPlayer, unsigned short wWeaponCategory, int nSpecializationLevel);

    // === Transformation State Management ===
    
    /**
     * @brief Checks if player is in siege mode
     * @param pPlayer Player to check
     * @return true if player is in siege mode
     */
    bool IsSiegeMode(CPlayer *pPlayer) const;

    /**
     * @brief Sets siege mode for player
     * @param pPlayer Player to set siege mode for
     * @param pItemData Item data for siege transformation
     */
    void SetSiege(CPlayer *pPlayer, _STORAGE_LIST::_db_con *pItemData);

    /**
     * @brief Releases siege mode for player
     * @param pPlayer Player to release siege mode for
     */
    void ReleaseSiegeMode(CPlayer *pPlayer);

    /**
     * @brief Gets current transformation type
     * @param pPlayer Player to get transformation for
     * @return Current transformation type
     */
    TransformationType GetCurrentTransformation(CPlayer *pPlayer) const;

    /**
     * @brief Checks if transformation is active
     * @param pPlayer Player to check
     * @return true if any transformation is active
     */
    bool IsTransformationActive(CPlayer *pPlayer) const;

    // === Validation Methods ===
    
    /**
     * @brief Validates siege mode transformation requirements
     * @param pPlayer Player to validate
     * @param pItemData Item data for transformation
     * @return Result code of validation
     */
    SiegeModeResult ValidateSiegeModeRequirements(CPlayer *pPlayer, _STORAGE_LIST::_storage_con *pItemData) const;

    /**
     * @brief Validates equipment compatibility for transformation
     * @param pPlayer Player to validate
     * @param pTransformItem Transformation item
     * @param pEquippedWeapon Currently equipped weapon
     * @return true if equipment is compatible
     */
    bool ValidateEquipmentCompatibility(CPlayer *pPlayer, _base_fld *pTransformItem, _base_fld *pEquippedWeapon) const;

    /**
     * @brief Validates civil equipment restrictions
     * @param nTableCode Item table code
     * @param wItemIndex Item index
     * @param byRaceSexCode Player race/sex code
     * @return true if item can be equipped by player
     */
    bool ValidateCivilEquipment(int nTableCode, int wItemIndex, char byRaceSexCode) const;

    /**
     * @brief Validates transformation item effects
     * @param pPlayer Player to validate
     * @param pItemData Item data to validate
     * @return true if item effects are valid
     */
    bool ValidateTransformationEffects(CPlayer *pPlayer, _STORAGE_LIST::_storage_con *pItemData) const;

    // === Timer and Duration Management ===
    
    /**
     * @brief Starts transformation timer
     * @param pPlayer Player to start timer for
     * @param dwDuration Duration in milliseconds
     */
    void StartTransformationTimer(CPlayer *pPlayer, DWORD dwDuration);

    /**
     * @brief Stops transformation timer
     * @param pPlayer Player to stop timer for
     */
    void StopTransformationTimer(CPlayer *pPlayer);

    /**
     * @brief Gets remaining transformation time
     * @param pPlayer Player to get time for
     * @return Remaining time in milliseconds
     */
    DWORD GetRemainingTransformationTime(CPlayer *pPlayer) const;

    // === Messaging and Notifications ===
    
    /**
     * @brief Sends siege mode transformation result
     * @param pPlayer Player to send result to
     * @param byResult Result code
     */
    void SendMsg_TransformSiegeModeResult(CPlayer *pPlayer, char byResult);

    /**
     * @brief Sends transformation status update
     * @param pPlayer Player to send update to
     * @param transformData Current transformation data
     */
    void SendTransformationStatusUpdate(CPlayer *pPlayer, const TransformationData &transformData);

protected:
    // === Core Configuration ===
    bool m_bInitialized;                             ///< Initialization status
    
    // === Configuration Constants ===
    static constexpr BYTE SIEGE_TABLE_CODE = 27;     ///< Required table code for siege items
    static constexpr DWORD DEFAULT_SIEGE_TIMER = 2000; ///< Default siege timer (2 seconds)
    static constexpr DWORD MAX_TRANSFORMATION_DURATION = 3600000; ///< Max transformation duration (1 hour)
    static constexpr int MAX_SPECIALIZATION_LEVEL = 10; ///< Maximum specialization level
    
    // === Security and Validation ===
    mutable std::mutex m_transformMutex;             ///< Mutex for thread-safe operations

private:
    // === Helper Methods ===
    
    /**
     * @brief Performs debug memory initialization
     * 
     * Initializes memory with debug pattern (0xCCCCCCCC) as per
     * decompiled source implementation.
     */
    void InitializeDebugMemory();

    /**
     * @brief Validates transformation parameters
     * @param pPlayer Player to validate
     * @param transformType Type of transformation
     * @return true if parameters are valid
     */
    bool ValidateTransformationParameters(CPlayer *pPlayer, TransformationType transformType) const;

    /**
     * @brief Processes transformation state change
     * @param pPlayer Player undergoing transformation
     * @param transformData New transformation data
     */
    void ProcessTransformationStateChange(CPlayer *pPlayer, const TransformationData &transformData);

    /**
     * @brief Logs transformation operation for audit
     * @param pPlayer Player performing operation
     * @param strOperation Operation description
     * @param transformType Optional transformation type
     */
    void LogTransformationOperation(CPlayer* pPlayer, const char* strOperation, TransformationType transformType = TRANSFORM_NONE) const;

    /**
     * @brief Validates player state for transformation
     * @param pPlayer Player to validate
     * @return true if player can transform
     */
    bool ValidatePlayerStateForTransformation(CPlayer *pPlayer) const;

    /**
     * @brief Applies transformation effects to player
     * @param pPlayer Player to apply effects to
     * @param transformData Transformation data
     */
    void ApplyTransformationEffects(CPlayer *pPlayer, const TransformationData &transformData);

    /**
     * @brief Removes transformation effects from player
     * @param pPlayer Player to remove effects from
     * @param transformType Type of transformation to remove
     */
    void RemoveTransformationEffects(CPlayer *pPlayer, TransformationType transformType);
};
