#pragma once

/**
 * @file CBillingManager.h
 * @brief Billing and Payment Management System for NexusPro
 * 
 * Manages player billing operations, payment validation, PC bang billing,
 * personal billing, IP overflow protection, and billing expiration handling.
 * Integrates with nation settings and provides secure payment processing.
 * 
 * Original decompiled functions:
 * - Billing_Logout: 0x140067CA0
 * - Expire_PCBang: 0x14028D2B0
 * - BillingExpirePersonal: Various addresses
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @category Player Extended Systems - Phase 2 Batch 7
 * @note STRICT ADHERENCE TO DECOMPILED SOURCE: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */

#include "../common/WindowsTypes.h"
#include "../common/RFProtocol.h"
#include "../common/Stubs.h"
#include "../nation/CNationSettingManager.h"
#include "../network/CNetworkEX.h"
#include <memory>
#include <mutex>
#include <string>
#include <chrono>

// Forward declarations
class CPlayer;
class CNationSettingManager;
class CNetworkEX;

/**
 * @enum BillingType
 * @brief Types of billing systems
 */
enum BillingType {
    BILLING_FREE = 0,                                ///< Free billing
    BILLING_PERSONAL = 1,                            ///< Personal billing
    BILLING_PCBANG = 2,                              ///< PC Bang billing
    BILLING_IP_OVERFLOW = 3,                         ///< IP overflow billing
    BILLING_PREMIUM = 4                              ///< Premium billing
};

/**
 * @enum BillingExpireReason
 * @brief Reasons for billing expiration
 */
enum BillingExpireReason {
    EXPIRE_TIMEOUT = 0,                              ///< Time-based expiration
    EXPIRE_MANUAL = 1,                               ///< Manual expiration
    EXPIRE_SYSTEM = 2,                               ///< System-forced expiration
    EXPIRE_IP_OVERFLOW = 3,                          ///< IP overflow expiration
    EXPIRE_PCBANG_CLOSE = 4                          ///< PC Bang closure expiration
};

/**
 * @struct BillingInfo
 * @brief Billing information structure
 */
struct BillingInfo {
    BillingType iType;                               ///< Billing type
    DWORD dwStartTime;                               ///< Billing start time
    DWORD dwEndTime;                                 ///< Billing end time
    DWORD dwRemainingTime;                           ///< Remaining billing time
    std::string strCMS;                              ///< CMS identifier for PC Bang
    bool bActive;                                    ///< Billing active status
    
    /**
     * @brief Default constructor
     */
    BillingInfo();
    
    /**
     * @brief Constructor with parameters
     */
    BillingInfo(BillingType type, DWORD startTime, DWORD endTime);
};

/**
 * @class CBillingManager
 * @brief Manages billing and payment operations for players
 * 
 * This class handles all aspects of billing functionality including:
 * - Player billing logout and session management
 * - PC Bang billing expiration and CMS validation
 * - Personal billing expiration handling
 * - IP overflow protection and billing
 * - Billing force close delay management
 * - Integration with nation settings and network systems
 * 
 * Based on decompiled source at: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */
class CBillingManager {
public:
    /**
     * @brief Default constructor
     * Initializes billing manager with RAII resource management
     */
    CBillingManager();

    /**
     * @brief Virtual destructor for proper cleanup
     * Ensures all billing operations are properly completed and resources released
     */
    virtual ~CBillingManager();

    // === Core Billing Operations ===
    
    /**
     * @brief Handles player billing logout
     * 
     * Processes billing logout including:
     * - Nation setting manager integration
     * - Billing force close delay retrieval
     * - Billing expiration notification sending
     * - Force close reservation
     * - Debug memory initialization (0xCCCCCCCC pattern)
     * 
     * @param pPlayer Player to logout from billing
     * 
     * Original address: 0x140067CA0
     * Function: ?Billing_Logout@CPlayer@@QEAAXXZ
     */
    void Billing_Logout(CPlayer *pPlayer);

    /**
     * @brief Expires PC Bang billing for specific CMS
     * 
     * Handles PC Bang billing expiration including:
     * - CMS identifier validation
     * - Player iteration and matching
     * - Billing type update to indicate expiration
     * - Comprehensive security validation
     * - Operator privilege verification
     * 
     * @param szCMS CMS identifier string for PC Bang to expire
     * 
     * Original address: 0x14028D2B0
     * Function: ?Expire_PCBang@CBilling@@QEAAXPEAD@Z
     */
    void Expire_PCBang(const char *szCMS);

    /**
     * @brief Handles personal billing expiration
     * 
     * Processes personal billing expiration for individual players.
     * 
     * @param pPlayer Player with expiring personal billing
     * @param reason Reason for expiration
     */
    void BillingExpirePersonal(CPlayer *pPlayer, BillingExpireReason reason);

    /**
     * @brief Handles IP overflow billing expiration
     * 
     * Processes IP overflow billing expiration and protection.
     * 
     * @param pPlayer Player with IP overflow billing
     */
    void BillingExpireIPOverflow(CPlayer *pPlayer);

    // === Billing Validation ===
    
    /**
     * @brief Validates billing status for player
     * @param pPlayer Player to validate
     * @return true if billing is valid and active
     */
    bool ValidateBillingStatus(CPlayer *pPlayer) const;

    /**
     * @brief Checks if player has active billing
     * @param pPlayer Player to check
     * @return true if player has active billing
     */
    bool HasActiveBilling(CPlayer *pPlayer) const;

    /**
     * @brief Gets billing type for player
     * @param pPlayer Player to get billing type for
     * @return Current billing type
     */
    BillingType GetBillingType(CPlayer *pPlayer) const;

    /**
     * @brief Gets remaining billing time
     * @param pPlayer Player to get remaining time for
     * @return Remaining billing time in seconds
     */
    DWORD GetRemainingBillingTime(CPlayer *pPlayer) const;

    // === PC Bang Management ===
    
    /**
     * @brief Validates PC Bang CMS identifier
     * @param szCMS CMS identifier to validate
     * @return true if CMS identifier is valid
     */
    bool ValidatePCBangCMS(const char *szCMS) const;

    /**
     * @brief Gets PC Bang billing information
     * @param pPlayer Player to get PC Bang info for
     * @return PC Bang billing information
     */
    BillingInfo GetPCBangBillingInfo(CPlayer *pPlayer) const;

    /**
     * @brief Sets PC Bang billing for player
     * @param pPlayer Player to set PC Bang billing for
     * @param szCMS CMS identifier
     * @param dwDuration Billing duration in seconds
     * @return true if PC Bang billing was set successfully
     */
    bool SetPCBangBilling(CPlayer *pPlayer, const char *szCMS, DWORD dwDuration);

    // === Force Close Management ===
    
    /**
     * @brief Reserves force close for player
     * @param pPlayer Player to reserve force close for
     */
    void ReservationForceClose(CPlayer *pPlayer);

    /**
     * @brief Gets billing force close delay
     * @return Force close delay in seconds
     */
    WORD GetBillingForceCloseDelay() const;

    /**
     * @brief Sends billing expiration notification
     * @param pPlayer Player to send notification to
     * @param nType Notification type
     * @param wDelay Delay before force close
     */
    void SendMsg_BillingExipreInform(CPlayer *pPlayer, int nType, WORD wDelay);

    // === Network Integration ===
    
    /**
     * @brief Handles billing expiration network requests
     * @param pNetworkEX Network system reference
     * @param pData Request data
     * @return Processing result
     */
    bool HandleBillingExpireRequest(CNetworkEX *pNetworkEX, const char *pData);

    /**
     * @brief Processes billing update from network
     * @param pPlayer Player to update billing for
     * @param billingInfo New billing information
     * @return true if update was successful
     */
    bool ProcessBillingUpdate(CPlayer *pPlayer, const BillingInfo &billingInfo);

    // === Configuration and Settings ===
    
    /**
     * @brief Sets nation setting manager reference
     * @param pNationManager Nation setting manager
     */
    void SetNationSettingManager(CNationSettingManager *pNationManager);

    /**
     * @brief Gets nation setting manager reference
     * @return Pointer to nation setting manager
     */
    CNationSettingManager* GetNationSettingManager() const { return m_pNationManager; }

protected:
    // === Core System Data ===
    CNationSettingManager* m_pNationManager;        ///< Nation setting manager reference
    CNetworkEX* m_pNetworkSystem;                    ///< Network system reference
    
    // === Billing State ===
    bool m_bInitialized;                             ///< Initialization status
    DWORD m_dwLastUpdate;                            ///< Last billing update timestamp
    
    // === Configuration Constants ===
    static constexpr DWORD DEFAULT_FORCE_CLOSE_DELAY = 30; ///< Default force close delay (30 seconds)
    static constexpr DWORD MAX_CMS_LENGTH = 64;      ///< Maximum CMS identifier length
    static constexpr DWORD BILLING_UPDATE_INTERVAL = 60000; ///< Billing update interval (1 minute)
    static constexpr DWORD MAX_BILLING_DURATION = 86400; ///< Maximum billing duration (24 hours)
    
    // === Security and Validation ===
    mutable std::mutex m_billingMutex;               ///< Mutex for thread-safe operations

private:
    // === Helper Methods ===
    
    /**
     * @brief Performs debug memory initialization
     * 
     * Initializes memory with debug pattern (0xCCCCCCCC) as per
     * decompiled source implementation.
     */
    void InitializeDebugMemory();

    /**
     * @brief Validates billing parameters
     * @param pPlayer Player to validate
     * @param billingType Billing type to validate
     * @return true if parameters are valid
     */
    bool ValidateBillingParameters(CPlayer *pPlayer, BillingType billingType) const;

    /**
     * @brief Updates billing expiration time
     * @param pPlayer Player to update
     * @param dwNewExpiration New expiration time
     */
    void UpdateBillingExpiration(CPlayer *pPlayer, DWORD dwNewExpiration);

    /**
     * @brief Logs billing operation for audit
     * @param pPlayer Player performing operation
     * @param strOperation Operation description
     * @param billingType Optional billing type
     */
    void LogBillingOperation(CPlayer* pPlayer, const char* strOperation, BillingType billingType = BILLING_FREE) const;

    /**
     * @brief Validates CMS string format and content
     * @param szCMS CMS string to validate
     * @return true if CMS string is valid
     */
    bool ValidateCMSString(const char *szCMS) const;

    /**
     * @brief Processes billing expiration cleanup
     * @param pPlayer Player to cleanup billing for
     */
    void ProcessBillingCleanup(CPlayer *pPlayer);

    /**
     * @brief Sends billing status update to client
     * @param pPlayer Player to send update to
     * @param billingInfo Current billing information
     */
    void SendBillingStatusUpdate(CPlayer *pPlayer, const BillingInfo &billingInfo);
};
