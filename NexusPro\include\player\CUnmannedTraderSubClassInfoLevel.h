﻿#pragma once

/**
 * @file CUnmannedTraderSubClassInfoLevel.h
 * @brief Unmanned Trader Level-based Classification System for NexusPro
 *
 * Manages level-based classification for unmanned traders, including
 * minimum and maximum level requirements, trader categorization,
 * and level-specific trading restrictions.
 *
 * Original decompiled functions:
 * - Constructor: 0x140384070
 * - Destructor: 0x1403840F0
 * - Create: Various addresses
 *
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @category Player Extended Systems - Phase 2 Batch 6
 * @note STRICT ADHERENCE TO DECOMPILED SOURCE: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */

#include "../common/WindowsTypes.h"
#include "../common/RFProtocol.h"
#include "../common/Stubs.h"
#include "../xml/TiXmlElement.h"
#include <memory>
#include <cstring>

// Forward declarations
class CLogFile;
class CUnmannedTraderSubClassInfo;
class CUnmannedTraderSubClassInfoCode;
class TiXmlElement;

/**
 * @class CUnmannedTraderSubClassInfoLevel
 * @brief Level-based classification for unmanned traders
 *
 * This class extends CUnmannedTraderSubClassInfo to provide level-based
 * categorization for unmanned traders. It manages:
 * - Minimum and maximum level requirements
 * - Level-based trader classification
 * - Trading restrictions based on player levels
 * - Level validation for trader operations
 *
 * Based on decompiled source at: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */
class CUnmannedTraderSubClassInfoLevel : public CUnmannedTraderSubClassInfo {
public:
    /**
     * @brief Default constructor
     * Initializes level-based trader classification with default settings
     */
    CUnmannedTraderSubClassInfoLevel();

    /**
     * @brief Constructor with ID parameter
     *
     * Initializes level-based trader classification with specified ID.
     * Uses debug memory initialization pattern (0xCCCCCCCC) as per decompiled source.
     * Sets default name to "level" and initializes min/max levels to 0.
     *
     * @param dwID Classification identifier
     *
     * Original address: 0x140384070
     * Function: ??0CUnmannedTraderSubClassInfoLevel@@QEAA@K@Z
     */
    CUnmannedTraderSubClassInfoLevel(unsigned int dwID);

    /**
     * @brief Virtual destructor for proper cleanup
     *
     * Ensures proper cleanup of level-based classification resources.
     *
     * Original address: 0x1403840F0
     * Function: ??1CUnmannedTraderSubClassInfoLevel@@QEAA@XZ
     */
    virtual ~CUnmannedTraderSubClassInfoLevel();

    // === Core Operations ===

    /**
     * @brief Creates and initializes classification with ID
     *
     * Factory method to create and initialize level-based classification
     * with the specified identifier.
     *
     * @param dwID Classification identifier
     */
    void Create(unsigned int dwID);

    /**
     * @brief Gets group ID for item classification
     *
     * Determines the appropriate group ID based on item table information
     * and classification parameters.
     *
     * @param byTableCode Item table code
     * @param wItemTableIndex Item table index
     * @param bySubClass Output subclass identifier
     * @return Group ID or error code
     */
    char GetGroupID(char byTableCode, unsigned __int16 wItemTableIndex, char *bySubClass);

    /**
     * @brief Loads classification data from XML
     *
     * Parses XML element to load level-based classification configuration
     * including min/max levels and other parameters.
     *
     * @param elemSubClass XML element containing classification data
     * @param kLogger Log file for error reporting
     * @param dwDivisionID Division identifier
     * @param dwClassID Class identifier
     * @return Success status of XML loading
     */
    char LoadXML(TiXmlElement *elemSubClass, CLogFile *kLogger, unsigned int dwDivisionID, unsigned int dwClassID);

    // === Level Management ===

    /**
     * @brief Gets minimum level requirement
     * @return Minimum level for this classification
     */
    BYTE GetMinLevel() const { return m_byMin; }

    /**
     * @brief Sets minimum level requirement
     * @param byMin New minimum level
     */
    void SetMinLevel(BYTE byMin) { m_byMin = byMin; }

    /**
     * @brief Gets maximum level requirement
     * @return Maximum level for this classification
     */
    BYTE GetMaxLevel() const { return m_byMax; }

    /**
     * @brief Sets maximum level requirement
     * @param byMax New maximum level
     */
    void SetMaxLevel(BYTE byMax) { m_byMax = byMax; }

    /**
     * @brief Gets classification name
     * @return Classification name string
     */
    const char* GetName() const { return m_szName; }

    /**
     * @brief Validates if player level fits this classification
     * @param byPlayerLevel Player's current level
     * @return true if player level is within range
     */
    bool ValidatePlayerLevel(BYTE byPlayerLevel) const;

protected:
    // === Core Level Data (based on decompiled source) ===
    BYTE m_byMin;                                    ///< Minimum level requirement
    BYTE m_byMax;                                    ///< Maximum level requirement
    char m_szName[32];                               ///< Classification name ("level" by default)

    // === Configuration ===
    static constexpr BYTE DEFAULT_MIN_LEVEL = 0;     ///< Default minimum level
    static constexpr BYTE DEFAULT_MAX_LEVEL = 0;     ///< Default maximum level
    static constexpr size_t MAX_NAME_LENGTH = 31;    ///< Maximum name length (excluding null terminator)

private:
    // === Helper Methods ===

    /**
     * @brief Performs debug memory initialization
     *
     * Initializes memory with debug pattern (0xCCCCCCCC) as per
     * decompiled source implementation.
     */
    void InitializeDebugMemory();

    /**
     * @brief Safe string copy with bounds checking
     * @param szDest Destination buffer
     * @param szSrc Source string
     * @param nMaxLen Maximum length including null terminator
     */
    void SafeStringCopy(char* szDest, const char* szSrc, size_t nMaxLen);
};
