#pragma once

/*
 * CFileManager.h - RESOURCE ENHANCED
 * NexusPro RF Online Zone Server
 * 
 * File system operations and management
 * Enhanced with Emergency Fix Methodology for RF Online compatibility
 * 
 * Original functionality based on decompiled RF Online server code
 * Enhanced with modern C++ standards and comprehensive file management
 */

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CMainThread;
class CResourceManager;
class CDataFileManager;
class CSystemManager;

// File operation types
enum FileOperationType {
    FILE_OP_READ = 0,
    FILE_OP_WRITE = 1,
    FILE_OP_APPEND = 2,
    FILE_OP_CREATE = 3,
    FILE_OP_DELETE = 4,
    FILE_OP_COPY = 5,
    FILE_OP_MOVE = 6,
    FILE_OP_RENAME = 7
};

// File access modes
enum FileAccessMode {
    FILE_ACCESS_READ = 0x01,
    FILE_ACCESS_WRITE = 0x02,
    FILE_ACCESS_READWRITE = 0x03,
    FILE_ACCESS_EXECUTE = 0x04,
    FILE_ACCESS_ALL = 0x07
};

// File types
enum FileType {
    FILE_TYPE_UNKNOWN = 0,
    FILE_TYPE_DATA = 1,
    FILE_TYPE_CONFIG = 2,
    FILE_TYPE_SCRIPT = 3,
    FILE_TYPE_TEXTURE = 4,
    FILE_TYPE_MODEL = 5,
    FILE_TYPE_SOUND = 6,
    FILE_TYPE_EFFECT = 7,
    FILE_TYPE_LOG = 8,
    FILE_TYPE_BACKUP = 9
};

// File information structure
struct FileInfo {
    char szFilePath[MAX_PATH];
    char szFileName[256];
    char szExtension[32];
    DWORD dwFileSize;
    FILETIME ftCreated;
    FILETIME ftModified;
    FILETIME ftAccessed;
    DWORD dwAttributes;
    FileType fileType;
    bool bExists;
    bool bReadOnly;
    bool bHidden;
    bool bSystem;
    bool bArchive;
    DWORD dwChecksum;
};

// File operation result
enum FileResult {
    FILE_SUCCESS = 0,
    FILE_ERROR_NOT_FOUND = 1,
    FILE_ERROR_ACCESS_DENIED = 2,
    FILE_ERROR_ALREADY_EXISTS = 3,
    FILE_ERROR_DISK_FULL = 4,
    FILE_ERROR_INVALID_PATH = 5,
    FILE_ERROR_SHARING_VIOLATION = 6,
    FILE_ERROR_CORRUPTED = 7,
    FILE_ERROR_UNKNOWN = 8
};

// File monitoring callback
typedef void (*FileMonitorCallback)(const char* szFilePath, DWORD dwAction, void* pUserData);

// File cache entry
struct FileCacheEntry {
    char szFilePath[MAX_PATH];
    void* pData;
    DWORD dwSize;
    DWORD dwLastAccess;
    DWORD dwAccessCount;
    bool bDirty;
    bool bLocked;
};

// File manager configuration
struct FileManagerConfig {
    bool bEnabled;
    bool bCacheEnabled;
    bool bMonitoringEnabled;
    bool bCompressionEnabled;
    bool bEncryptionEnabled;
    bool bBackupEnabled;
    DWORD dwMaxCacheSize;
    DWORD dwMaxCacheEntries;
    DWORD dwCacheTimeout;
    DWORD dwMonitorInterval;
    DWORD dwBackupInterval;
    char szRootPath[MAX_PATH];
    char szBackupPath[MAX_PATH];
    char szTempPath[MAX_PATH];
};

// File manager statistics
struct FileManagerStatistics {
    DWORD dwTotalOperations;
    DWORD dwSuccessfulOperations;
    DWORD dwFailedOperations;
    DWORD dwReadOperations;
    DWORD dwWriteOperations;
    DWORD dwDeleteOperations;
    DWORD dwCopyOperations;
    DWORD dwMoveOperations;
    DWORD dwCacheHits;
    DWORD dwCacheMisses;
    DWORD dwBytesRead;
    DWORD dwBytesWritten;
    DWORD dwFilesMonitored;
    DWORD dwBackupsCreated;
    DWORD dwAverageOperationTime;
    DWORD dwMaxOperationTime;
    DWORD dwLastOperationTime;
};

/*
 * CFileManager - File system operations and management
 * Manages all file operations, caching, and monitoring
 * Enhanced with comprehensive file management and RF Online compatibility
 */
class CFileManager {
public:
    // Singleton access
    static CFileManager& Instance();

    // Core lifecycle methods
    bool Initialize();
    void Shutdown();
    void Update();

    // File operation methods
    FileResult ReadFile(const char* szFilePath, void** ppData, DWORD* pdwSize);
    FileResult WriteFile(const char* szFilePath, const void* pData, DWORD dwSize);
    FileResult AppendFile(const char* szFilePath, const void* pData, DWORD dwSize);
    FileResult CreateFile(const char* szFilePath);
    FileResult DeleteFile(const char* szFilePath);
    FileResult CopyFile(const char* szSourcePath, const char* szDestPath);
    FileResult MoveFile(const char* szSourcePath, const char* szDestPath);
    FileResult RenameFile(const char* szOldPath, const char* szNewPath);

    // File information methods
    bool GetFileInfo(const char* szFilePath, FileInfo& info);
    bool FileExists(const char* szFilePath);
    DWORD GetFileSize(const char* szFilePath);
    FILETIME GetFileModifiedTime(const char* szFilePath);
    FileType GetFileType(const char* szFilePath);
    DWORD CalculateFileChecksum(const char* szFilePath);

    // Directory operations
    bool CreateDirectory(const char* szDirPath);
    bool DeleteDirectory(const char* szDirPath, bool bRecursive = false);
    bool EnumerateFiles(const char* szDirPath, std::vector<std::string>& files, const char* szPattern = "*.*");
    bool EnumerateDirectories(const char* szDirPath, std::vector<std::string>& directories);

    // File caching methods
    bool CacheFile(const char* szFilePath);
    void UncacheFile(const char* szFilePath);
    bool IsFileCached(const char* szFilePath);
    void* GetCachedFileData(const char* szFilePath, DWORD* pdwSize);
    void FlushCache();
    void OptimizeCache();

    // File monitoring methods
    bool StartFileMonitoring(const char* szPath, FileMonitorCallback callback, void* pUserData);
    bool StopFileMonitoring(const char* szPath);
    bool IsPathMonitored(const char* szPath);

    // File backup methods
    bool CreateBackup(const char* szFilePath);
    bool RestoreBackup(const char* szFilePath);
    bool DeleteBackup(const char* szFilePath);
    bool HasBackup(const char* szFilePath);

    // Path utility methods
    bool IsValidPath(const char* szPath);
    bool IsAbsolutePath(const char* szPath);
    bool IsRelativePath(const char* szPath);
    void NormalizePath(char* szPath);
    void GetFileName(const char* szFilePath, char* szFileName, DWORD dwBufferSize);
    void GetFileExtension(const char* szFilePath, char* szExtension, DWORD dwBufferSize);
    void GetDirectoryPath(const char* szFilePath, char* szDirPath, DWORD dwBufferSize);

    // Configuration and statistics
    const FileManagerConfig& GetConfig() const { return m_Config; }
    const FileManagerStatistics& GetStatistics() const { return m_Statistics; }
    void ResetStatistics();

    // Utility methods
    bool IsFileManagerEnabled() const { return m_bInitialized && m_Config.bEnabled; }
    void LogFileOperation(FileOperationType operation, const char* szPath, FileResult result);

private:
    // Private constructor for singleton
    CFileManager();
    ~CFileManager();

    // Prevent copying
    CFileManager(const CFileManager&) = delete;
    CFileManager& operator=(const CFileManager&) = delete;

    // Internal methods
    bool LoadConfiguration();
    bool InitializeCache();
    bool InitializeMonitoring();
    bool InitializeBackupSystem();
    void ShutdownCache();
    void ShutdownMonitoring();
    void ShutdownBackupSystem();
    FileResult ProcessFileOperation(FileOperationType operation, const char* szPath);
    void UpdateStatistics();
    void UpdateCache();
    void UpdateMonitoring();
    void UpdateBackupSystem();

    // Cache management
    FileCacheEntry* FindCacheEntry(const char* szFilePath);
    FileCacheEntry* CreateCacheEntry(const char* szFilePath, const void* pData, DWORD dwSize);
    void RemoveCacheEntry(const char* szFilePath);
    void CleanupExpiredCacheEntries();

    // File type detection
    FileType DetectFileType(const char* szFilePath);
    bool IsDataFile(const char* szExtension);
    bool IsConfigFile(const char* szExtension);
    bool IsScriptFile(const char* szExtension);
    bool IsTextureFile(const char* szExtension);
    bool IsModelFile(const char* szExtension);
    bool IsSoundFile(const char* szExtension);

    // Member variables
    static CFileManager* s_pInstance;
    static std::mutex s_InstanceMutex;

    bool m_bInitialized;
    bool m_bCacheActive;
    bool m_bMonitoringActive;
    bool m_bBackupActive;
    FileManagerConfig m_Config;
    FileManagerStatistics m_Statistics;

    // File cache
    std::map<std::string, FileCacheEntry> m_FileCache;
    DWORD m_dwCurrentCacheSize;
    DWORD m_dwCacheEntryCount;

    // File monitoring
    std::map<std::string, FileMonitorCallback> m_MonitorCallbacks;
    std::map<std::string, void*> m_MonitorUserData;
    std::vector<std::string> m_MonitoredPaths;

    // Synchronization
    mutable std::mutex m_ConfigMutex;
    mutable std::mutex m_StatisticsMutex;
    mutable std::mutex m_CacheMutex;
    mutable std::mutex m_MonitorMutex;
    mutable std::mutex m_OperationMutex;

    // Performance tracking
    DWORD m_dwLastUpdateTime;
    DWORD m_dwUpdateInterval;
    DWORD m_dwOperationStartTime;
};
