﻿#pragma once

/**
 * @file CReturnGateController.h
 * @brief Return Gate Management System for NexusPro
 *
 * Manages return gate operations, including gate creation, player entry,
 * ownership validation, and gate lifecycle management. Return gates allow
 * players to create temporary portals for quick travel.
 *
 * Original decompiled functions:
 * - Enter: 0x1402508B0
 * - Open: 0x1402506A0
 * - ProcessEnter: Various addresses
 *
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @category Player Extended Systems - Phase 2 Batch 6
 * @note STRICT ADHERENCE TO DECOMPILED SOURCE: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */

#include "../common/WindowsTypes.h"
#include "../common/RFProtocol.h"
#include "../common/Stubs.h"
#include "../network/CNetIndexList.h"
#include "../common/CLogFile.h"
#include <memory>
#include <mutex>

// Forward declarations
class CLogFile;
class CMyTimer;
class CNetCriticalSection;
class CNetIndexList;
class CNetProcess;
class CPlayer;
class CPlayerDB;
class CReturnGate;
class CReturnGateCreateParam;

/**
 * @class CReturnGateController
 * @brief Manages return gate operations and player portal system
 *
 * This class handles all aspects of return gate functionality including:
 * - Gate creation and ownership management
 * - Player entry validation and processing
 * - Gate lifecycle and resource management
 * - Index list management for available/used gates
 * - Security validation and guild battle restrictions
 *
 * Based on decompiled source at: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 */
class CReturnGateController {
public:
    /**
     * @brief Default constructor
     * Initializes return gate controller with RAII resource management
     */
    CReturnGateController();

    /**
     * @brief Virtual destructor for proper cleanup
     * Ensures all gates are properly closed and resources released
     */
    virtual ~CReturnGateController();

    // === Core Gate Operations ===

    /**
     * @brief Processes player entry into return gate
     *
     * Validates gate index, processes entry request, and sends result.
     * Uses debug memory initialization pattern (0xCCCCCCCC) as per decompiled source.
     *
     * @param uiGateInx Gate index to enter
     * @param pkObj Player attempting to enter
     * @return 1 on success, 0 on failure
     *
     * Original address: 0x1402508B0
     * Function: ?Enter@CReturnGateController@@QEAA_NIPEAVCPlayer@@@Z
     */
    char Enter(unsigned int uiGateInx, CPlayer *pkObj);

    /**
     * @brief Opens a new return gate for player
     *
     * Creates and opens a return gate for the specified player owner.
     * Validates guild battle status, checks existing ownership, and manages
     * gate allocation from empty/used index lists.
     *
     * @param pkOwner Player who will own the gate
     * @return 1 on success, 0 on failure
     *
     * Original address: 0x1402506A0
     * Function: ?Open@CReturnGateController@@QEAA_NPEAVCPlayer@@@Z
     */
    char Open(CPlayer *pkOwner);

    /**
     * @brief Processes gate entry request
     *
     * Internal processing function that handles the actual gate entry logic
     * and validation before sending results.
     *
     * @param uiGateInx Gate index for entry
     * @param pkObj Player requesting entry
     * @return Result code (0 = success, non-zero = error)
     */
    int ProcessEnter(unsigned int uiGateInx, CPlayer *pkObj);

    /**
     * @brief Checks if player already owns a gate
     * @param pkObj Player to check ownership for
     * @return 1 if player owns a gate, 0 otherwise
     */
    char IsExistOwner(CPlayer *pkObj);

    /**
     * @brief Sends gate entry result to player
     * @param iResult Result code to send
     * @param pkObj Target player
     */
    void SendEnterResult(int iResult, CPlayer *pkObj);

    /**
     * @brief Initializes return gate controller system
     * @param uiSize Size parameter for initialization
     * @return Success status of initialization
     */
    char Init(unsigned int uiSize);

    // === Extended Operations ===

    /**
     * @brief Gets gate by index
     * @param uiIndex Gate index
     * @return Pointer to gate, nullptr if invalid
     */
    CReturnGate* GetGate(unsigned int uiIndex) const;

    /**
     * @brief Gets number of active gates
     * @return Count of currently active gates
     */
    size_t GetActiveGateCount() const;

    /**
     * @brief Emergency shutdown of all gates
     */
    void EmergencyShutdown();

private:
    // === Core Gate Management ===
    std::unique_ptr<CReturnGate[]> m_pGates;         ///< Array of return gates
    std::unique_ptr<CNetIndexList> m_pEmptyList;     ///< List of empty gate indices
    std::unique_ptr<CNetIndexList> m_pUsedList;      ///< List of used gate indices

    // === System Configuration ===
    unsigned int m_uiMaxSize;                        ///< Maximum number of gates
    bool m_bInitialized;                             ///< Initialization status

    // === Security and Validation ===
    mutable std::mutex m_gateMutex;                  ///< Mutex for thread-safe operations

    // === Configuration Constants ===
    static constexpr unsigned int DEFAULT_MAX_GATES = 100; ///< Default maximum gates
    static constexpr DWORD GATE_LIFETIME_MS = 300000;      ///< Gate lifetime (5 minutes)

    // === Helper Methods ===

    /**
     * @brief Validates gate index
     * @param uiIndex Index to validate
     * @return true if index is valid
     */
    bool ValidateGateIndex(unsigned int uiIndex) const;

    /**
     * @brief Finds gate owned by player
     * @param pkPlayer Player to find gate for
     * @return Gate index, INVALID_INDEX if not found
     */
    unsigned int FindPlayerGate(CPlayer* pkPlayer) const;

    /**
     * @brief Validates player permissions for gate operations
     * @param pkPlayer Player to validate
     * @return true if player has permissions
     */
    bool ValidatePlayerPermissions(CPlayer* pkPlayer) const;

    /**
     * @brief Logs gate operation for audit
     * @param pkPlayer Player performing operation
     * @param strOperation Operation description
     */
    void LogGateOperation(CPlayer* pkPlayer, const char* strOperation) const;

    static constexpr unsigned int INVALID_INDEX = 0xFFFFFFFF; ///< Invalid gate index marker
};
